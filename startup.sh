#!/bin/bash
# 学生出勤管理系统启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

# 创建必要目录
mkdir -p logs pids

# 显示系统信息
show_banner() {
    clear
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}   学生出勤管理系统${NC}"
    echo -e "${CYAN}================================${NC}"
    echo -e "${GREEN}系统架构:${NC}"
    echo -e "  Kafka生产者(5秒) → Spark流处理 → MySQL → Web可视化"
    echo -e "  Kafka(B组) → Flume → HDFS → Sqoop → Hive"
    echo -e "${GREEN}核心功能:${NC}"
    echo -e "  • 实时数据生产和处理"
    echo -e "  • 协同过滤推荐"
    echo -e "  • 数据可视化展示"
    echo ""
}

# 检查服务状态
check_status() {
    echo -e "${YELLOW}当前服务状态:${NC}"

    if pgrep -f "kafka_producer.py" > /dev/null; then
        echo -e "${GREEN}✓ Kafka生产者 [运行中]${NC}"
    else
        echo -e "${RED}✗ Kafka生产者 [已停止]${NC}"
    fi

    if pgrep -f "spark_streaming_consumer.py" > /dev/null; then
        echo -e "${GREEN}✓ Spark流处理 [运行中]${NC}"
    else
        echo -e "${RED}✗ Spark流处理 [已停止]${NC}"
    fi

    if pgrep -f "spark_rdd_processor.py" > /dev/null; then
        echo -e "${GREEN}✓ RDD处理 [运行中]${NC}"
    else
        echo -e "${RED}✗ RDD处理 [已停止]${NC}"
    fi

    if pgrep -f "spark_sql_processor.py" > /dev/null; then
        echo -e "${GREEN}✓ SQL处理 [运行中]${NC}"
    else
        echo -e "${RED}✗ SQL处理 [已停止]${NC}"
    fi

    if pgrep -f "collaborative_filtering.py" > /dev/null; then
        echo -e "${GREEN}✓ 协同过滤 [运行中]${NC}"
    else
        echo -e "${RED}✗ 协同过滤 [已停止]${NC}"
    fi

    if pgrep -f "web_app.py" > /dev/null; then
        echo -e "${GREEN}✓ Web应用 [运行中]${NC}"
    else
        echo -e "${RED}✗ Web应用 [已停止]${NC}"
    fi
    echo ""
}

# 显示菜单
show_menu() {
    show_banner
    check_status
    
    echo -e "${CYAN}请选择操作:${NC}"
    echo -e "${CYAN}[1] 启动Kafka生产者${NC}"
    echo -e "${CYAN}[2] 启动Spark流处理${NC}"
    echo -e "${CYAN}[3] 运行RDD处理${NC}"
    echo -e "${CYAN}[4] 运行Spark SQL处理${NC}"
    echo -e "${CYAN}[5] 运行协同过滤${NC}"
    echo -e "${CYAN}[6] 启动Web应用${NC}"
    echo -e "${CYAN}[7] 启动Flume采集${NC}"
    echo -e "${CYAN}[8] 运行Sqoop抽取${NC}"
    echo -e "${GREEN}[9] 一键启动所有服务${NC}"
    echo -e "${YELLOW}[10] 停止所有服务${NC}"
    echo -e "${RED}[0] 退出${NC}"
    echo ""
}

# 启动Kafka生产者
start_kafka_producer() {
    echo -e "${YELLOW}启动Kafka生产者...${NC}"
    nohup python3 kafka_producer.py > logs/kafka_producer.log 2>&1 &
    echo $! > pids/kafka_producer.pid
    echo -e "${GREEN}✓ Kafka生产者已启动${NC}"
    sleep 2
}

# 启动Spark流处理
start_spark_streaming() {
    echo -e "${YELLOW}启动Spark流处理...${NC}"
    nohup python3 spark_streaming_consumer.py > logs/spark_streaming.log 2>&1 &
    echo $! > pids/spark_streaming.pid
    echo -e "${GREEN}✓ Spark流处理已启动${NC}"
    sleep 2
}

# 运行RDD处理
run_rdd_processing() {
    echo -e "${YELLOW}运行RDD处理...${NC}"
    nohup python3 spark_rdd_processor.py > logs/spark_rdd.log 2>&1 &
    echo $! > pids/spark_rdd.pid
    echo -e "${GREEN}✓ RDD处理已启动${NC}"
    sleep 2
}

# 运行Spark SQL处理
run_spark_sql() {
    echo -e "${YELLOW}运行Spark SQL处理...${NC}"
    nohup python3 spark_sql_processor.py > logs/spark_sql.log 2>&1 &
    echo $! > pids/spark_sql.pid
    echo -e "${GREEN}✓ Spark SQL处理已启动${NC}"
    sleep 2
}

# 运行协同过滤
run_collaborative_filtering() {
    echo -e "${YELLOW}运行协同过滤...${NC}"
    nohup python3 collaborative_filtering.py > logs/collaborative_filtering.log 2>&1 &
    echo $! > pids/collaborative_filtering.pid
    echo -e "${GREEN}✓ 协同过滤已启动${NC}"
    sleep 2
}

# 启动Web应用
start_web_app() {
    echo -e "${YELLOW}启动Web应用...${NC}"
    echo -e "${GREEN}访问地址: http://localhost:5001${NC}"
    echo -e "${GREEN}默认账户: admin/admin123${NC}"
    echo "按Ctrl+C停止Web应用"
    python3 web_app.py
}

# 启动Flume采集
start_flume() {
    echo -e "${YELLOW}启动Flume数据采集...${NC}"
    if command -v flume-ng &> /dev/null; then
        nohup flume-ng agent --conf conf --conf-file flume_config.conf --name agent > logs/flume.log 2>&1 &
        echo $! > pids/flume.pid
        echo -e "${GREEN}✓ Flume已启动${NC}"
    else
        echo -e "${RED}✗ Flume未安装${NC}"
    fi
}

# 运行Sqoop抽取
run_sqoop() {
    echo -e "${YELLOW}运行Sqoop数据抽取...${NC}"
    if command -v sqoop &> /dev/null; then
        ./sqoop_import.sh
        echo -e "${GREEN}✓ Sqoop抽取完成${NC}"
    else
        echo -e "${RED}✗ Sqoop未安装${NC}"
    fi
}

# 一键启动所有服务
start_all_services() {
    echo -e "${YELLOW}一键启动所有服务...${NC}"
    
    # 停止现有服务
    pkill -f "kafka_producer.py" 2>/dev/null
    pkill -f "spark_streaming_consumer.py" 2>/dev/null
    sleep 2
    
    # 启动核心服务
    start_kafka_producer
    start_spark_streaming
    run_rdd_processing
    run_spark_sql
    run_collaborative_filtering
    
    echo ""
    echo -e "${GREEN}✓ 所有后台服务已启动${NC}"
    echo -e "${CYAN}现在启动Web应用...${NC}"
    echo -e "${CYAN}访问地址: http://localhost:5001${NC}"
    echo -e "${CYAN}默认账户: admin/admin123${NC}"
    read -p "按回车键启动Web应用..."
    python3 web_app.py
}

# 停止所有服务
stop_all_services() {
    echo -e "${YELLOW}停止所有服务...${NC}"

    pkill -f "kafka_producer.py" 2>/dev/null
    pkill -f "spark_streaming_consumer.py" 2>/dev/null
    pkill -f "spark_rdd_processor.py" 2>/dev/null
    pkill -f "spark_sql_processor.py" 2>/dev/null
    pkill -f "collaborative_filtering.py" 2>/dev/null
    pkill -f "web_app.py" 2>/dev/null
    pkill -f "flume-ng" 2>/dev/null

    rm -f pids/*.pid

    echo -e "${GREEN}✓ 所有服务已停止${NC}"
    sleep 2
}

# 主程序循环
while true; do
    show_menu
    read -p "请输入选项 [0-10]: " choice
    
    case $choice in
        1) start_kafka_producer; read -p "按回车键继续..." ;;
        2) start_spark_streaming; read -p "按回车键继续..." ;;
        3) run_rdd_processing; read -p "按回车键继续..." ;;
        4) run_spark_sql; read -p "按回车键继续..." ;;
        5) run_collaborative_filtering; read -p "按回车键继续..." ;;
        6) start_web_app ;;
        7) start_flume; read -p "按回车键继续..." ;;
        8) run_sqoop; read -p "按回车键继续..." ;;
        9) start_all_services ;;
        10) stop_all_services; read -p "按回车键继续..." ;;
        0) echo -e "${GREEN}退出系统${NC}"; exit 0 ;;
        *) echo -e "${RED}无效选项，请重新选择${NC}"; sleep 1 ;;
    esac
done
