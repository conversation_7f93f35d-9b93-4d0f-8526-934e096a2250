{% extends "base.html" %}

{% block title %}离线推荐 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">离线推荐系统</h2>
        <p class="text-muted">基于历史数据统计，展示缺席最多的学生排名</p>
    </div>
</div>

<!-- 控制面板 -->
<div class="row mb-4">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i> 推荐设置
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="limit-select" class="form-label">显示学生数量:</label>
                        <select class="form-select" id="limit-select" onchange="loadOfflineRecommendations()">
                            <option value="5">前5名</option>
                            <option value="10" selected>前10名</option>
                            <option value="15">前15名</option>
                            <option value="20">前20名</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-primary" onclick="loadOfflineRecommendations()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 离线推荐结果 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i> 缺席排行榜
                </h5>
                <small>基于历史出勤数据的离线统计分析</small>
            </div>
            <div class="card-body">
                <div id="offline-recommendations">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载离线推荐数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计图表 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i> 缺勤统计图表
                </h5>
            </div>
            <div class="card-body">
                <div id="absence-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 算法说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 离线推荐算法说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">算法特点</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 基于历史出勤数据统计</li>
                            <li><i class="fas fa-check text-success"></i> 离线批量处理计算</li>
                            <li><i class="fas fa-check text-success"></i> 定期更新排名结果</li>
                            <li><i class="fas fa-check text-success"></i> 支持多维度排序</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">应用价值</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 识别高风险学生群体</li>
                            <li><i class="fas fa-check text-success"></i> 支持教学管理决策</li>
                            <li><i class="fas fa-check text-success"></i> 提供干预优先级参考</li>
                            <li><i class="fas fa-check text-success"></i> 历史趋势分析</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6 class="text-primary">排名计算公式</h6>
                    <div class="bg-light p-3 rounded">
                        <code>
                            排名 = ORDER BY (总缺勤次数 DESC, 缺勤率 DESC)<br>
                            缺勤率 = (缺勤次数 / 总记录数) × 100%
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadOfflineRecommendations();

    // 每5秒更新一次离线推荐数据 (虽然叫离线，但数据流还是跟着Kafka更新的)
    setInterval(loadOfflineRecommendations, 5000);
});

// 加载离线推荐数据
function loadOfflineRecommendations() {
    const limit = document.getElementById('limit-select').value;
    
    fetch(`/api/offline_recommendations?limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            displayOfflineRecommendations(data);
            displayAbsenceChart(data);
        })
        .catch(error => {
            console.error('加载离线推荐失败:', error);
            document.getElementById('offline-recommendations').innerHTML =
                '<div class="text-center"><i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i><p class="text-danger mt-3">加载失败，请稍后重试</p></div>';
        });
}

// 显示离线推荐结果
function displayOfflineRecommendations(data) {
    const container = document.getElementById('offline-recommendations');

    if (data.length === 0) {
        container.innerHTML = `
            <div class="text-center">
                <i class="fas fa-info-circle text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-3">暂无推荐数据</p>
                <small class="text-muted">系统正在收集学生出勤数据，请稍后再试</small>
            </div>
        `;
        return;
    }

    // 创建排行榜表格
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="10%">排名</th>
                        <th width="15%">学号</th>
                        <th width="15%">姓名</th>
                        <th width="15%">缺勤次数</th>
                        <th width="15%">缺勤率</th>
                        <th width="15%">风险等级</th>
                        <th width="15%">操作建议</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.forEach((student, index) => {
        const rank = student.rank_order || (index + 1);
        const absenceRate = parseFloat(student.absence_rate) || 0;
        
        // 根据排名和缺勤率确定风险等级
        let riskLevel, riskClass, riskIcon, suggestion;
        if (rank <= 3 || absenceRate >= 50) {
            riskLevel = '极高风险';
            riskClass = 'danger';
            riskIcon = 'fas fa-exclamation-triangle';
            suggestion = '立即干预';
        } else if (rank <= 6 || absenceRate >= 30) {
            riskLevel = '高风险';
            riskClass = 'warning';
            riskIcon = 'fas fa-exclamation-circle';
            suggestion = '重点关注';
        } else if (rank <= 10 || absenceRate >= 20) {
            riskLevel = '中风险';
            riskClass = 'info';
            riskIcon = 'fas fa-info-circle';
            suggestion = '定期跟进';
        } else {
            riskLevel = '低风险';
            riskClass = 'success';
            riskIcon = 'fas fa-check-circle';
            suggestion = '正常监控';
        }

        html += `
            <tr>
                <td>
                    <span class="badge bg-${riskClass} fs-6">
                        ${rank <= 3 ? '<i class="fas fa-trophy"></i> ' : ''}#${rank}
                    </span>
                </td>
                <td><code>${student.student_id}</code></td>
                <td><strong>${student.student_name}</strong></td>
                <td>
                    <span class="badge bg-danger">${student.total_absence || 0}次</span>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-${riskClass}" role="progressbar" 
                             style="width: ${Math.min(absenceRate, 100)}%">
                            ${absenceRate.toFixed(1)}%
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${riskClass}">
                        <i class="${riskIcon}"></i> ${riskLevel}
                    </span>
                </td>
                <td>
                    <small class="text-${riskClass}">
                        <i class="fas fa-lightbulb"></i> ${suggestion}
                    </small>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    // 添加统计摘要
    const highRiskCount = data.filter((_, i) => i < 3).length;
    const mediumRiskCount = data.filter((_, i) => i >= 3 && i < 6).length;
    const lowRiskCount = data.length - highRiskCount - mediumRiskCount;

    html += `
        <div class="mt-4 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-4">
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                        <h4 class="mb-0">${highRiskCount}</h4>
                        <small>极高风险学生</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-warning">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                        <h4 class="mb-0">${mediumRiskCount}</h4>
                        <small>高风险学生</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-info">
                        <i class="fas fa-info-circle fa-2x"></i>
                        <h4 class="mb-0">${lowRiskCount}</h4>
                        <small>中低风险学生</small>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// 显示缺勤统计图表
function displayAbsenceChart(data) {
    if (data.length === 0) return;

    const chart = echarts.init(document.getElementById('absence-chart'));
    
    const studentNames = data.map(item => item.student_name);
    const absenceCounts = data.map(item => item.total_absence || 0);
    const absenceRates = data.map(item => parseFloat(item.absence_rate) || 0);

    const option = {
        title: {
            text: '学生缺勤统计',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            }
        },
        legend: {
            data: ['缺勤次数', '缺勤率'],
            top: 30
        },
        xAxis: {
            type: 'category',
            data: studentNames,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '缺勤次数',
                position: 'left'
            },
            {
                type: 'value',
                name: '缺勤率(%)',
                position: 'right'
            }
        ],
        series: [
            {
                name: '缺勤次数',
                type: 'bar',
                data: absenceCounts,
                itemStyle: {color: '#dc3545'}
            },
            {
                name: '缺勤率',
                type: 'line',
                yAxisIndex: 1,
                data: absenceRates,
                itemStyle: {color: '#ffc107'},
                lineStyle: {color: '#ffc107'}
            }
        ]
    };

    chart.setOption(option);
}
</script>
{% endblock %}
