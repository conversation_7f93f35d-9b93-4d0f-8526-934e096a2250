{% extends "base.html" %}

{% block title %}协同过滤推荐 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">协同过滤推荐系统</h2>
        <p class="text-muted">基于协同过滤算法的个性化推荐，展示缺勤风险前6名学生</p>
    </div>
</div>

<!-- 控制面板 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog"></i> 实时推荐控制
                        </h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group" role="group">
                            <button class="btn btn-primary btn-sm" onclick="loadPersonalizedRecommendations()" id="refresh-btn">
                                <i class="fas fa-sync-alt"></i> 刷新CF推荐
                            </button>

                            <button class="btn btn-success btn-sm" onclick="toggleAutoRefresh()" id="auto-refresh-btn">
                                <i class="fas fa-play"></i> 开启自动刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator me-2" id="data-status">
                                <i class="fas fa-circle text-secondary"></i>
                            </div>
                            <div>
                                <small class="text-muted">数据状态</small>
                                <div id="status-text">等待加载</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <i class="fas fa-clock text-info"></i>
                            </div>
                            <div>
                                <small class="text-muted">最后更新</small>
                                <div id="last-update">未更新</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <div class="me-2">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                            <div>
                                <small class="text-muted">推荐学生数</small>
                                <div id="student-count">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 协同过滤推荐 -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> 协同过滤推荐 TOP6
                </h5>
                <small>基于用户-课程关系矩阵和协同过滤算法推荐缺勤风险前6名学生</small>
            </div>
            <div class="card-body">
                <div id="personalized-recommendations">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载推荐数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 协同过滤算法说明 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 协同过滤算法说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">算法原理</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 建立用户-课程关系矩阵</li>
                            <li><i class="fas fa-check text-success"></i> 建立课程-课程相似度矩阵</li>
                            <li><i class="fas fa-check text-success"></i> 计算用户相似度矩阵</li>
                            <li><i class="fas fa-check text-success"></i> 基于User-Base CF生成推荐列表</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">应用效果</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 识别缺勤风险较高的学生</li>
                            <li><i class="fas fa-check text-success"></i> 基于历史行为模式预测</li>
                            <li><i class="fas fa-check text-success"></i> 提供个性化关注建议</li>
                            <li><i class="fas fa-check text-success"></i> 支持教学管理决策</li>
                        </ul>
                    </div>
                </div>

                <div class="mt-4">
                    <h6 class="text-primary">数据矩阵示例</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">用户-课程矩阵 (4×7)</small>
                            <div class="bg-light p-2 rounded mt-1">
                                <code style="font-size: 0.8em;">
                                学生ID    高等数学  线性代数  概率论  ...<br>
                                2021CS001    1       0       0    ...<br>
                                2021CS002    1       0       0    ...<br>
                                2021EE001    0       0       1    ...<br>
                                2022CS001    0       1       1    ...
                                </code>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">课程相似度矩阵 (7×7)</small>
                            <div class="bg-light p-2 rounded mt-1">
                                <code style="font-size: 0.8em;">
                                课程      高等数学  线性代数  概率论<br>
                                高等数学    1.0     0.0     0.5<br>
                                线性代数    0.0     1.0     0.3<br>
                                概率论     0.5     0.3     1.0
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadPersonalizedRecommendations();

    updateStatus('loading', '正在加载数据...');
});

// 更新状态显示
function updateStatus(status, message) {
    const statusIndicator = document.getElementById('data-status');
    const statusText = document.getElementById('status-text');

    // 清除所有状态类
    statusIndicator.innerHTML = '';

    switch(status) {
        case 'loading':
            statusIndicator.innerHTML = '<i class="fas fa-spinner fa-spin text-primary"></i>';
            statusText.textContent = message || '加载中...';
            break;
        case 'success':
            statusIndicator.innerHTML = '<i class="fas fa-circle text-success"></i>';
            statusText.textContent = message || '数据已更新';
            break;
        case 'error':
            statusIndicator.innerHTML = '<i class="fas fa-circle text-danger"></i>';
            statusText.textContent = message || '加载失败';
            break;
        case 'processing':
            statusIndicator.innerHTML = '<i class="fas fa-cog fa-spin text-warning"></i>';
            statusText.textContent = message || '处理中...';
            break;
        default:
            statusIndicator.innerHTML = '<i class="fas fa-circle text-secondary"></i>';
            statusText.textContent = message || '等待加载';
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('last-update').textContent = timeString;
}

// 更新学生数量
function updateStudentCount(count) {
    document.getElementById('student-count').textContent = count;
}

// 加载协同过滤推荐
function loadPersonalizedRecommendations() {
    // 更新按钮状态
    const refreshBtn = document.getElementById('refresh-btn');
    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';

    updateStatus('loading', '正在获取推荐数据...');

    fetch('/api/personalized_recommendations?limit=6')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            const container = document.getElementById('personalized-recommendations');

            // 更新状态和计数
            updateStatus('success', '数据加载成功');
            updateLastUpdateTime();
            updateStudentCount(data.length);

            if (data.length === 0) {
                container.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-info-circle text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">暂无推荐数据</p>
                        <small class="text-muted">系统正在学习用户行为模式，请稍后再试</small>
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="loadPersonalizedRecommendations()">
                                <i class="fas fa-sync-alt"></i> 重新加载
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            let html = '<div class="row">';
            data.forEach((student, index) => {
                const badgeClass = index < 3 ? 'danger' : index < 6 ? 'warning' : 'secondary';
                const riskLevel = index < 3 ? '高风险' : index < 6 ? '中风险' : '低风险';
                const riskIcon = index < 3 ? 'fas fa-exclamation-triangle' :
                               index < 6 ? 'fas fa-exclamation-circle' : 'fas fa-info-circle';

                html += `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100 border-${badgeClass === 'danger' ? 'danger' : badgeClass === 'warning' ? 'warning' : 'light'}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">${student.student_name}</h6>
                                    <span class="badge bg-${badgeClass}">
                                        <i class="${riskIcon}"></i> ${riskLevel}
                                    </span>
                                </div>
                                <p class="card-text">
                                    <small class="text-muted">学号: ${student.student_id}</small><br>
                                    <small class="text-muted">缺勤次数: ${student.absence_count || 0}次</small><br>
                                    <small class="text-muted">推荐排名: ${index + 1}</small>
                                </p>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-${badgeClass}" role="progressbar"
                                         style="width: ${Math.max(10, 100 - index * 8)}%">
                                    </div>
                                </div>
                                <small class="text-muted">缺勤风险指数</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            // 添加统计信息
            html += `
                <div class="mt-4 p-3 bg-light rounded">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h5 class="mb-0">${data.filter((_, i) => i < 3).length}</h5>
                                <small>高风险学生</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-warning">
                                <i class="fas fa-exclamation-circle"></i>
                                <h5 class="mb-0">${data.filter((_, i) => i >= 3 && i < 6).length}</h5>
                                <small>中风险学生</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-secondary">
                                <i class="fas fa-info-circle"></i>
                                <h5 class="mb-0">${data.filter((_, i) => i >= 6).length}</h5>
                                <small>低风险学生</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        })
        .catch(error => {
            console.error('加载协同过滤推荐失败:', error);
            updateStatus('error', '加载失败');
            updateStudentCount(0);

            document.getElementById('personalized-recommendations').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    <p class="text-danger mt-3">加载失败，请稍后重试</p>
                    <small class="text-muted">错误信息: ${error.message}</small>
                    <div class="mt-3">
                        <button class="btn btn-danger" onclick="loadPersonalizedRecommendations()">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                </div>
            `;
        })
        .finally(() => {
            // 恢复按钮状态
            const refreshBtn = document.getElementById('refresh-btn');
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新数据';
        });
}

// 切换自动刷新
function toggleAutoRefresh() {
    const btn = document.getElementById('auto-refresh-btn');

    if (isAutoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        isAutoRefreshEnabled = false;
        btn.innerHTML = '<i class="fas fa-play"></i> 开启自动刷新';
        btn.className = 'btn btn-success btn-sm';
        updateStatus('success', '自动刷新已停止');
    } else {
        // 开启自动刷新 - 与Kafka推送频率同步
        autoRefreshInterval = setInterval(() => {
            loadPersonalizedRecommendations();
            console.log('推荐数据已更新 - ' + new Date().toLocaleTimeString());
        }, 5000); // 每5秒刷新一次

        isAutoRefreshEnabled = true;
        btn.innerHTML = '<i class="fas fa-pause"></i> 停止自动刷新';
        btn.className = 'btn btn-warning btn-sm';
        updateStatus('processing', '自动刷新已开启 (5秒间隔)');
    }
}



// 显示协同过滤推荐结果
function displayPersonalizedRecommendations(data) {
    const container = document.getElementById('personalized-recommendations');

    if (data.length === 0) {
        container.innerHTML = `
            <div class="text-center">
                <i class="fas fa-info-circle text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-3">暂无协同过滤推荐数据</p>
                <small class="text-muted">系统正在学习用户行为模式，请稍后再试</small>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="loadPersonalizedRecommendations()">
                        <i class="fas fa-sync-alt"></i> 重新加载
                    </button>
                </div>
            </div>
        `;
        return;
    }

    let html = '<div class="row">';
    data.forEach((student, index) => {
        const score = student.recommendation_score || 0;
        const scorePercent = Math.min(score * 100, 100);
        const badgeClass = scorePercent >= 80 ? 'success' : scorePercent >= 60 ? 'warning' : 'danger';
        const riskLevel = scorePercent >= 80 ? '推荐' : scorePercent >= 60 ? '一般' : '不推荐';

        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 border-${badgeClass}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${student.student_name}</h6>
                            <span class="badge bg-${badgeClass}">
                                <i class="fas fa-star"></i> ${riskLevel}
                            </span>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">学号: ${student.student_id}</small><br>
                            <small class="text-muted">推荐课程: ${student.course_name}</small><br>
                            <small class="text-muted">CF排名: ${index + 1}</small>
                        </p>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-${badgeClass}" role="progressbar"
                                 style="width: ${scorePercent}%">
                            </div>
                        </div>
                        <small class="text-muted">推荐分数: ${score.toFixed(4)}</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    // 添加协同过滤算法信息
    html += `
        <div class="mt-4 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-4">
                    <div class="text-info">
                        <i class="fas fa-users fa-2x"></i>
                        <h5 class="mb-0">CF</h5>
                        <small>协同过滤算法</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-primary">
                        <i class="fas fa-chart-line fa-2x"></i>
                        <h5 class="mb-0">${data.length}</h5>
                        <small>推荐结果</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-success">
                        <i class="fas fa-check-circle fa-2x"></i>
                        <h5 class="mb-0">User-Based</h5>
                        <small>基于用户</small>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}



// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>
{% endblock %}
