{% extends "base.html" %}

{% block title %}学生搜索 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">学生搜索与预测</h2>
        <p class="text-muted">搜索目标学生，预测可能缺席的TOP3课程</p>
    </div>
</div>

<!-- 搜索表单 -->
<div class="row mb-4">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i> 学生搜索
                </h5>
            </div>
            <div class="card-body">
                <form id="search-form" onsubmit="searchStudent(event)">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" 
                               id="student-name" name="student_name" 
                               placeholder="请输入学生姓名..." required>
                        <button class="btn btn-primary btn-lg" type="submit">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="form-text">
                        支持模糊搜索，例如输入"张"可以搜索到所有姓张的学生
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="row" id="search-results" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> 学生信息
                </h5>
            </div>
            <div class="card-body">
                <div id="student-info"></div>
            </div>
        </div>
    </div>
</div>

<!-- 预测结果 -->
<div class="row mt-4" id="prediction-results" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-crystal-ball"></i> 缺席预测 TOP3
                </h5>
                <small>基于历史出勤数据的智能预测</small>
            </div>
            <div class="card-body">
                <div id="predictions-content"></div>
            </div>
        </div>
    </div>
</div>

<!-- 预测算法说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 预测算法说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">预测原理</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 分析学生历史出勤记录</li>
                            <li><i class="fas fa-check text-success"></i> 计算各课程的缺勤率</li>
                            <li><i class="fas fa-check text-success"></i> 基于缺勤模式进行预测</li>
                            <li><i class="fas fa-check text-success"></i> 识别高风险课程</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">应用场景</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> 提前干预缺勤风险</li>
                            <li><i class="fas fa-check text-success"></i> 个性化学习指导</li>
                            <li><i class="fas fa-check text-success"></i> 课程安排优化</li>
                            <li><i class="fas fa-check text-success"></i> 学生关怀服务</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="row" id="loading-indicator" style="display: none;">
    <div class="col-12">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">搜索中...</span>
            </div>
            <p class="mt-3 text-muted">正在搜索学生信息...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 搜索学生
function searchStudent(event) {
    event.preventDefault();
    
    const studentName = document.getElementById('student-name').value.trim();
    if (!studentName) {
        alert('请输入学生姓名');
        return;
    }
    
    // 显示加载状态
    showLoading();
    hideResults();
    
    // 发送搜索请求
    fetch(`/api/search_student?student_name=${encodeURIComponent(studentName)}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.error) {
                showError(data.error);
                return;
            }
            
            // 显示学生信息
            showStudentInfo(data.student_info);
            
            // 显示预测结果
            showPredictions(data.predictions);
        })
        .catch(error => {
            hideLoading();
            console.error('搜索失败:', error);
            showError('搜索失败，请稍后重试');
        });
}

// 显示学生信息
function showStudentInfo(studentInfo) {
    const container = document.getElementById('student-info');
    
    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">基本信息</h6>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>学号:</strong></td>
                        <td>${studentInfo.student_id}</td>
                    </tr>
                    <tr>
                        <td><strong>姓名:</strong></td>
                        <td>${studentInfo.student_name}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>提示:</strong> 基于该学生的历史出勤数据进行缺席预测
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('search-results').style.display = 'block';
}

// 显示预测结果
function showPredictions(predictions) {
    const container = document.getElementById('predictions-content');
    
    if (!predictions || predictions.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle"></i>
                该学生暂无足够的历史数据进行预测
            </div>
        `;
    } else {
        let html = '<div class="row">';
        
        predictions.forEach((prediction, index) => {
            const rankClass = index === 0 ? 'danger' : index === 1 ? 'warning' : 'info';
            const riskLevel = index === 0 ? '高风险' : index === 1 ? '中风险' : '低风险';
            
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card border-${rankClass}">
                        <div class="card-header bg-${rankClass} text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-trophy"></i> TOP ${index + 1}
                            </h6>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">${prediction.course_name}</h5>
                            <p class="card-text">
                                <span class="badge bg-${rankClass}">${riskLevel}</span>
                            </p>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-${rankClass}" role="progressbar" 
                                     style="width: ${prediction.absence_rate}%">
                                    ${prediction.absence_rate}%
                                </div>
                            </div>
                            <small class="text-muted">
                                历史缺勤率: ${prediction.absence_rate}%
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        
        if (predictions.length < 3) {
            html += `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>注意:</strong> 该学生的历史数据较少，预测结果仅供参考
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    document.getElementById('prediction-results').style.display = 'block';
}

// 显示加载状态
function showLoading() {
    document.getElementById('loading-indicator').style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loading-indicator').style.display = 'none';
}

// 隐藏结果
function hideResults() {
    document.getElementById('search-results').style.display = 'none';
    document.getElementById('prediction-results').style.display = 'none';
}

// 显示错误信息
function showError(message) {
    const container = document.getElementById('search-results');
    container.innerHTML = `
        <div class="col-12">
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-circle"></i>
                ${message}
            </div>
        </div>
    `;
    container.style.display = 'block';
}

// 回车键搜索
document.getElementById('student-name').addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        searchStudent(event);
    }
});
</script>
{% endblock %}
