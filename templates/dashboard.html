{% extends "base.html" %}

{% block title %}首页 - 学生出勤管理系统{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section text-center mb-5">
    <div class="container">
        <h1 class="display-4 mb-4">欢迎使用学生出勤管理系统</h1>
        <p class="lead">实时监控学生出勤情况，基于大数据技术栈构建的智能化管理平台</p>
    </div>
</div>

<!-- 统计卡片 - 优化版 -->
<div class="row mb-5">
    <div class="col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon text-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-number" id="total-present">-</div>
            <div class="stats-label">总出勤数</div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon text-danger">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stats-number" id="total-absent">-</div>
            <div class="stats-label">总缺勤数</div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <!-- 出勤缺勤总和饼图 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">出勤缺勤总和统计</h5>
            </div>
            <div class="card-body">
                <div id="attendance-pie-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
    
    <!-- 课程出勤TOP5柱状图 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">课程出勤TOP5</h5>
            </div>
            <div class="card-body">
                <div id="course-top5-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 班级出勤统计表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">班级出勤统计</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="class-attendance-table">
                        <thead class="table-dark">
                            <tr>
                                <th>班级号</th>
                                <th>出勤人次</th>
                                <th>缺勤人次</th>
                                <th>出勤率</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center text-muted">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    正在加载数据...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAttendanceSummary();
    loadAttendancePieChart();
    loadCourseTop5Chart();
    loadClassAttendanceTable();
    
    // 每5秒刷新一次数据 - 与Kafka推送频率同步
    setInterval(function() {
        loadAttendanceSummary();
        loadAttendancePieChart();
        loadCourseTop5Chart();
        loadClassAttendanceTable();
        console.log('数据已更新 - ' + new Date().toLocaleTimeString());
    }, 5000);
});

// 加载出勤总和数据
function loadAttendanceSummary() {
    fetch('/api/attendance_summary')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-present').textContent = data.total_present || 0;
            document.getElementById('total-absent').textContent = data.total_absent || 0;
        })
        .catch(error => console.error('加载出勤总和数据失败:', error));
}

// 加载出勤饼图
function loadAttendancePieChart() {
    fetch('/api/attendance_summary')
        .then(response => response.json())
        .then(data => {
            const chart = echarts.init(document.getElementById('attendance-pie-chart'));
            
            const option = {
                title: {
                    text: '出勤情况分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '出勤统计',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            {value: data.total_present || 0, name: '出勤', itemStyle: {color: '#28a745'}},
                            {value: data.total_absent || 0, name: '缺勤', itemStyle: {color: '#dc3545'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };
            
            chart.setOption(option);
        })
        .catch(error => console.error('加载饼图数据失败:', error));
}

// 加载课程TOP5柱状图
function loadCourseTop5Chart() {
    fetch('/api/course_top5')
        .then(response => response.json())
        .then(data => {
            const chart = echarts.init(document.getElementById('course-top5-chart'));
            
            const courseNames = data.map(item => item.course_name);
            const presentCounts = data.map(item => item.present_count);
            const absentCounts = data.map(item => item.absent_count);
            
            const option = {
                title: {
                    text: '课程出勤TOP5',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['出勤', '缺勤'],
                    top: 30
                },
                xAxis: {
                    type: 'category',
                    data: courseNames,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '出勤',
                        type: 'bar',
                        data: presentCounts,
                        itemStyle: {color: '#28a745'}
                    },
                    {
                        name: '缺勤',
                        type: 'bar',
                        data: absentCounts,
                        itemStyle: {color: '#dc3545'}
                    }
                ]
            };
            
            chart.setOption(option);
        })
        .catch(error => console.error('加载课程TOP5数据失败:', error));
}

// 加载班级出勤表格
function loadClassAttendanceTable() {
    fetch('/api/class_attendance')
        .then(response => response.json())
        .then(data => {
            console.log('班级出勤数据:', data); // 调试信息
            const tbody = document.querySelector('#class-attendance-table tbody');
            tbody.innerHTML = '';

            if (!data || data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
                return;
            }

            data.forEach(item => {
                const row = document.createElement('tr');
                const attendanceRate = parseFloat(item.attendance_rate) || 0;
                const statusClass = attendanceRate >= 80 ? 'success' : attendanceRate >= 60 ? 'warning' : 'danger';
                const statusText = attendanceRate >= 80 ? '良好' : attendanceRate >= 60 ? '一般' : '较差';

                row.innerHTML = `
                    <td>${item.class_number}</td>
                    <td>${item.present_count}</td>
                    <td>${item.absent_count}</td>
                    <td>${attendanceRate.toFixed(1)}%</td>
                    <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                `;
                tbody.appendChild(row);
            });
        })
        .catch(error => console.error('加载班级出勤表格失败:', error));
}
</script>
{% endblock %}
