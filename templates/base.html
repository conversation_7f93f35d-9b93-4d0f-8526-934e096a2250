<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}学生出勤管理系统{% endblock %}</title>

    <!-- 本地CSS文件 -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/fontawesome.min.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- 强制导航栏样式 - 专业前端优化版 -->
    <style>
        /* 导航栏基础样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
            border-bottom: 3px solid #667eea !important;
            min-height: 70px !important;
            padding: 0.8rem 0 !important;
        }

        /* 强制所有导航栏文字为深色 - 在白色背景上可见 */
        .navbar,
        .navbar *,
        .navbar a,
        .navbar .navbar-brand,
        .navbar .nav-link,
        .navbar .dropdown-toggle {
            color: #2c3e50 !important;
            text-decoration: none !important;
        }

        /* 导航栏图标颜色 */
        .navbar .fas,
        .navbar .fa {
            color: #2c3e50 !important;
        }

        /* 品牌标识样式 */
        .navbar .navbar-brand {
            font-weight: 700 !important;
            font-size: 1.6rem !important;
            color: #2c3e50 !important;
            text-shadow: none !important;
            transition: all 0.3s ease !important;
        }

        .navbar .navbar-brand:hover {
            transform: translateY(-1px) !important;
            color: #1a252f !important;
        }

        /* 导航链接样式 */
        .navbar .nav-link {
            font-weight: 500 !important;
            padding: 0.8rem 1rem !important;
            border-radius: 6px !important;
            transition: all 0.3s ease !important;
            margin: 0 0.2rem !important;
            color: #2c3e50 !important;
        }

        .navbar .nav-link:hover,
        .navbar .nav-link:focus {
            background-color: rgba(44, 62, 80, 0.1) !important;
            transform: translateY(-1px) !important;
            color: #1a252f !important;
        }

        /* 图标样式 */
        .navbar .fas,
        .navbar .fa {
            margin-right: 0.5rem !important;
            color: #ffffff !important;
        }

        /* 下拉菜单样式 */
        .navbar .dropdown-menu {
            background: rgba(255, 255, 255, 0.95) !important;
            border: none !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
            border-radius: 8px !important;
        }

        .navbar .dropdown-item {
            color: #333 !important;
            transition: all 0.3s ease !important;
        }

        .navbar .dropdown-item:hover {
            background-color: #667eea !important;
            color: #ffffff !important;
        }

        /* 移动端切换按钮 */
        .navbar-toggler {
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 6px !important;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
        }

        /* 响应式优化 */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background: rgba(0, 0, 0, 0.1) !important;
                border-radius: 8px !important;
                margin-top: 1rem !important;
                padding: 1rem !important;
            }
        }
    </style>

    {% block head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-graduation-cap"></i> 学生出勤管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="dataProcessingDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cogs"></i> 数据处理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('realtime') }}">
                                <i class="fas fa-broadcast-tower"></i> 21) 实时界面
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('offline') }}">
                                <i class="fas fa-database"></i> 22) 离线界面
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="recommendationDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-star"></i> 推荐系统
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('recommendations') }}">
                                <i class="fas fa-users"></i> 23) 实时推荐
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('offline_recommendations') }}">
                                <i class="fas fa-chart-line"></i> 24) 离线推荐
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search"></i> 25) 学生搜索
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.username %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ session.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">
                            <i class="fas fa-user-plus"></i> 注册
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 学生出勤管理系统. 基于大数据技术栈构建.</p>
        </div>
    </footer>

    <!-- 本地JS文件 -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
