{% extends "base.html" %}

{% block title %}离线界面 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">离线数据处理界面</h2>
        <p class="text-muted">Kafka(B组)消费 → Flume采集(HDFS) → Sqoop抽取(Hive) → Spark SQL → MySQL → Web可视化</p>
    </div>
</div>

<!-- 离线数据流状态 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-secondary">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i> 离线数据流状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-stream fa-2x text-primary" id="kafka-b-status"></i>
                            <p class="mt-2">Kafka(B组)</p>
                            <small class="text-muted">消费者组</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-cloud-upload-alt fa-2x text-info" id="flume-status"></i>
                            <p class="mt-2">Flume采集</p>
                            <small class="text-muted">到HDFS</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-warehouse fa-2x text-warning" id="hdfs-status"></i>
                            <p class="mt-2">HDFS存储</p>
                            <small class="text-muted">/home/<USER>/small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-exchange-alt fa-2x text-success" id="sqoop-status"></i>
                            <p class="mt-2">Sqoop抽取</p>
                            <small class="text-muted">到Hive</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-search fa-2x text-danger" id="spark-sql-status"></i>
                            <p class="mt-2">Spark SQL</p>
                            <small class="text-muted">离线处理</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-chart-bar fa-2x text-primary" id="web-offline-status"></i>
                            <p class="mt-2">Web展示</p>
                            <small class="text-muted">离线结果</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 离线处理结果 -->
<div class="row">
    <!-- Spark RDD班级统计 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cubes"></i> Spark RDD班级统计
                </h5>
                <small>使用Spark Core RDD统计各个班级号所有学生的出勤和缺勤数量</small>
            </div>
            <div class="card-body">
                <div id="rdd-class-chart" style="height: 350px;"></div>
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm" onclick="runRDDProcessing()">
                        <i class="fas fa-play"></i> 运行RDD处理
                    </button>
                    <span id="rdd-status" class="ml-2"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Spark SQL课程统计 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-database"></i> Spark SQL课程统计
                </h5>
                <small>使用Spark SQL统计各个课程号的出勤和缺勤数量</small>
            </div>
            <div class="card-body">
                <div id="sql-course-chart" style="height: 350px;"></div>
                <div class="mt-3">
                    <button class="btn btn-info btn-sm" onclick="runSQLProcessing()">
                        <i class="fas fa-play"></i> 运行SQL处理
                    </button>
                    <span id="sql-status" class="ml-2"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据仓库信息 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server"></i> Hive数据仓库信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary">数据库信息</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-database text-success"></i> 数据库名: attendance</li>
                            <li><i class="fas fa-table text-info"></i> 表数量: 5个</li>
                            <li><i class="fas fa-clock text-warning"></i> 最后更新: <span id="last-update">--</span></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary">数据表列表</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-table"></i> raw_attendance_data</li>
                            <li><i class="fas fa-table"></i> attendance_summary</li>
                            <li><i class="fas fa-table"></i> class_attendance</li>
                            <li><i class="fas fa-table"></i> course_attendance</li>
                            <li><i class="fas fa-table"></i> user_recommendations</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-primary">处理状态</h6>
                        <ul class="list-unstyled">
                            <li><span id="flume-process-status" class="badge badge-secondary">Flume: 待启动</span></li>
                            <li><span id="sqoop-process-status" class="badge badge-secondary">Sqoop: 待运行</span></li>
                            <li><span id="spark-process-status" class="badge badge-secondary">Spark: 待处理</span></li>
                        </ul>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success" onclick="runFullOfflinePipeline()">
                        <i class="fas fa-rocket"></i> 运行完整离线流水线
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initOfflineCharts();
    loadOfflineData();
    
    // 每5秒更新一次离线数据 (虽然叫离线，但数据流还是跟着Kafka更新的)
    setInterval(loadOfflineData, 5000);
});

// 初始化图表
function initOfflineCharts() {
    window.offlineCharts = {
        rdd: echarts.init(document.getElementById('rdd-class-chart')),
        sql: echarts.init(document.getElementById('sql-course-chart'))
    };
}

// 加载离线数据
function loadOfflineData() {
    loadRDDData();
    loadSQLData();
    updateLastUpdateTime();
}

// 加载RDD处理结果
function loadRDDData() {
    // 这里应该调用RDD处理结果的API
    fetch('/api/class_attendance')
        .then(response => response.json())
        .then(data => {
            const classes = data.map(item => item.class_number);
            const presentData = data.map(item => item.present_count);
            const absentData = data.map(item => item.absent_count);
            
            const option = {
                title: { text: 'RDD班级统计结果', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['出勤', '缺勤'], top: 30 },
                xAxis: { type: 'category', data: classes },
                yAxis: { type: 'value' },
                series: [
                    { name: '出勤', type: 'bar', data: presentData, itemStyle: { color: '#5cb85c' } },
                    { name: '缺勤', type: 'bar', data: absentData, itemStyle: { color: '#d9534f' } }
                ]
            };
            window.offlineCharts.rdd.setOption(option);
        });
}

// 加载SQL处理结果
function loadSQLData() {
    fetch('/api/course_top5')
        .then(response => response.json())
        .then(data => {
            const courses = data.map(item => item.course_name);
            const presentData = data.map(item => item.present_count);
            const absentData = data.map(item => item.absent_count);
            
            const option = {
                title: { text: 'SQL课程统计结果', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['出勤', '缺勤'], top: 30 },
                xAxis: { type: 'category', data: courses, axisLabel: { rotate: 30 } },
                yAxis: { type: 'value' },
                series: [
                    { name: '出勤', type: 'line', data: presentData, smooth: true },
                    { name: '缺勤', type: 'line', data: absentData, smooth: true }
                ]
            };
            window.offlineCharts.sql.setOption(option);
        });
}

// 运行RDD处理
function runRDDProcessing() {
    const statusEl = document.getElementById('rdd-status');
    statusEl.innerHTML = '<span class="text-warning"><i class="fas fa-spinner fa-spin"></i> 正在运行RDD处理...</span>';
    
    // 这里应该调用后端API触发RDD处理
    setTimeout(() => {
        statusEl.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> RDD处理完成</span>';
        loadRDDData();
    }, 3000);
}

// 运行SQL处理
function runSQLProcessing() {
    const statusEl = document.getElementById('sql-status');
    statusEl.innerHTML = '<span class="text-warning"><i class="fas fa-spinner fa-spin"></i> 正在运行SQL处理...</span>';
    
    // 这里应该调用后端API触发SQL处理
    setTimeout(() => {
        statusEl.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> SQL处理完成</span>';
        loadSQLData();
    }, 3000);
}

// 运行完整离线流水线
function runFullOfflinePipeline() {
    alert('启动完整离线数据处理流水线：\n1. Flume采集数据到HDFS\n2. Sqoop抽取数据到Hive\n3. Spark SQL处理数据\n4. 结果写入MySQL');
}

// 更新最后更新时间
function updateLastUpdateTime() {
    document.getElementById('last-update').textContent = new Date().toLocaleString();
}
</script>
{% endblock %}
