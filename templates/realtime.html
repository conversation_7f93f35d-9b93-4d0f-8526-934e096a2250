{% extends "base.html" %}

{% block title %}实时界面 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4">实时数据处理界面</h2>
        <p class="text-muted">Kafka生产者(每5秒) → Kafka(A组)消费 → Spark流处理 → MySQL → Web可视化</p>
    </div>
</div>

<!-- 系统状态指示器 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-broadcast-tower"></i> 实时数据流状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-play-circle fa-2x text-success" id="kafka-status"></i>
                            <p class="mt-2">Kafka生产者</p>
                            <small class="text-muted">每5秒推送</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-arrow-right fa-2x text-info"></i>
                            <p class="mt-2">数据传输</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-cogs fa-2x text-warning" id="spark-status"></i>
                            <p class="mt-2">Spark流处理</p>
                            <small class="text-muted">实时统计</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-arrow-right fa-2x text-info"></i>
                            <p class="mt-2">数据传输</p>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-database fa-2x text-success" id="mysql-status"></i>
                            <p class="mt-2">MySQL存储</p>
                            <small class="text-muted">attendance_db</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="status-indicator">
                            <i class="fas fa-chart-line fa-2x text-primary" id="web-status"></i>
                            <p class="mt-2">Web可视化</p>
                            <small class="text-muted">实时更新</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时统计数据 -->
<div class="row">
    <!-- 总体出勤统计 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> 实时总体统计
                </h5>
                <small>Spark Streaming实时统计所有学生出勤和缺勤数量总和</small>
            </div>
            <div class="card-body">
                <div id="realtime-total-chart" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <!-- 班级出勤统计 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-school"></i> 实时班级统计
                </h5>
                <small>Spark Streaming实时统计各个班级号的出勤和缺勤数量</small>
            </div>
            <div class="card-body">
                <div id="realtime-class-chart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 课程出勤统计 -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-book"></i> 实时课程统计
                </h5>
                <small>Spark Streaming实时统计各个课程的出勤和缺勤数量</small>
            </div>
            <div class="card-body">
                <div id="realtime-course-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 数据更新日志 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> 实时更新日志
                </h5>
            </div>
            <div class="card-body">
                <div id="update-log" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace;">
                    <div class="text-muted">等待数据更新...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initRealtimeCharts();
    startRealtimeUpdates();
});

// 初始化图表
function initRealtimeCharts() {
    // 初始化总体统计图表
    const totalChart = echarts.init(document.getElementById('realtime-total-chart'));
    
    // 初始化班级统计图表
    const classChart = echarts.init(document.getElementById('realtime-class-chart'));
    
    // 初始化课程统计图表
    const courseChart = echarts.init(document.getElementById('realtime-course-chart'));
    
    // 存储图表实例
    window.realtimeCharts = {
        total: totalChart,
        class: classChart,
        course: courseChart
    };
}

// 开始实时更新
function startRealtimeUpdates() {
    // 立即加载一次数据
    updateRealtimeData();
    
    // 每5秒更新一次 - 与Kafka推送频率同步
    setInterval(updateRealtimeData, 5000);
}

// 更新实时数据
function updateRealtimeData() {
    const timestamp = new Date().toLocaleTimeString();
    
    // 更新总体统计
    updateTotalStats(timestamp);
    
    // 更新班级统计
    updateClassStats(timestamp);
    
    // 更新课程统计
    updateCourseStats(timestamp);
    
    // 记录更新日志
    logUpdate(timestamp);
}

// 更新总体统计
function updateTotalStats(timestamp) {
    fetch('/api/attendance_summary')
        .then(response => response.json())
        .then(data => {
            const option = {
                title: { text: '总体出勤统计', left: 'center' },
                tooltip: { trigger: 'item' },
                series: [{
                    type: 'pie',
                    radius: '60%',
                    data: [
                        { value: data.total_present, name: '出勤' },
                        { value: data.total_absent, name: '缺勤' }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            window.realtimeCharts.total.setOption(option);
        });
}

// 更新班级统计
function updateClassStats(timestamp) {
    fetch('/api/class_attendance')
        .then(response => response.json())
        .then(data => {
            const classes = data.map(item => item.class_number);
            const presentData = data.map(item => item.present_count);
            const absentData = data.map(item => item.absent_count);
            
            const option = {
                title: { text: '班级出勤统计', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['出勤', '缺勤'], top: 30 },
                xAxis: { type: 'category', data: classes },
                yAxis: { type: 'value' },
                series: [
                    { name: '出勤', type: 'bar', data: presentData },
                    { name: '缺勤', type: 'bar', data: absentData }
                ]
            };
            window.realtimeCharts.class.setOption(option);
        });
}

// 更新课程统计
function updateCourseStats(timestamp) {
    fetch('/api/course_top5')
        .then(response => response.json())
        .then(data => {
            const courses = data.map(item => item.course_name);
            const presentData = data.map(item => item.present_count);
            const absentData = data.map(item => item.absent_count);
            
            const option = {
                title: { text: '课程出勤统计', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['出勤', '缺勤'], top: 30 },
                xAxis: { type: 'category', data: courses, axisLabel: { rotate: 45 } },
                yAxis: { type: 'value' },
                series: [
                    { name: '出勤', type: 'line', data: presentData },
                    { name: '缺勤', type: 'line', data: absentData }
                ]
            };
            window.realtimeCharts.course.setOption(option);
        });
}

// 记录更新日志
function logUpdate(timestamp) {
    const logContainer = document.getElementById('update-log');
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span class="text-success">[${timestamp}]</span> 实时数据已更新 - Kafka → Spark → MySQL → Web`;
    logContainer.appendChild(logEntry);
    
    // 保持最新20条日志
    while (logContainer.children.length > 20) {
        logContainer.removeChild(logContainer.firstChild);
    }
    
    // 滚动到底部
    logContainer.scrollTop = logContainer.scrollHeight;
}
</script>
{% endblock %}
