{% extends "base.html" %}

{% block title %}登录 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">用户登录</h4>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">登录</button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <p class="mb-0">还没有账户？ <a href="{{ url_for('register') }}">立即注册</a></p>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">
                        <strong>测试账户：</strong><br>
                        用户名: admin, 密码: admin123<br>
                        用户名: student1, 密码: student123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
