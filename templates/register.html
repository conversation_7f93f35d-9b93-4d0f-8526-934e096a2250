{% extends "base.html" %}

{% block title %}注册 - 学生出勤管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">用户注册</h4>
            </div>
            <div class="card-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="form-text">用户名将用于登录系统</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码 *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="real_name" class="form-label">真实姓名</label>
                        <input type="text" class="form-control" id="real_name" name="real_name">
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">注册</button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <p class="mb-0">已有账户？ <a href="{{ url_for('login') }}">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
