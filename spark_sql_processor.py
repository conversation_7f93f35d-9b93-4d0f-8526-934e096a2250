#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spark SQL处理器
需求222: 使用spark sql统计各个课程号的出勤和缺勤数量
"""

from pyspark.sql import SparkSession
import mysql.connector
from datetime import datetime

class SparkSQLProcessor:
    def __init__(self):
        # 初始化Spark Session
        self.spark = SparkSession.builder \
            .appName("AttendanceSQLProcessor") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .getOrCreate()

        # MySQL连接配置
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'attendance_db'
        }
    
    def ensure_data_flow_setup(self):
        """确保数据流的前置步骤已完成"""
        try:
            print("检查数据流前置步骤...")

            # 1. 检查并创建Hive数据库
            print("1. 检查Hive数据库...")
            try:
                self.spark.sql("USE attendance")
                print("   ✓ Hive数据库attendance已存在")
            except:
                print("   ✗ Hive数据库不存在，正在创建...")
                self.spark.sql("CREATE DATABASE IF NOT EXISTS attendance")
                print("   ✓ Hive数据库attendance已创建")

            # 2. 检查并创建Hive表
            print("2. 检查Hive表...")
            try:
                self.spark.sql("USE attendance")
                result = self.spark.sql("SHOW TABLES LIKE 'attendance_data'")
                if result.count() == 0:
                    print("   ✗ Hive表不存在，正在创建...")
                    self.spark.sql("""
                        CREATE TABLE IF NOT EXISTS attendance.attendance_data (
                            class_number STRING,
                            student_name STRING,
                            course_name STRING,
                            student_id STRING,
                            score INT,
                            attendance_status STRING
                        )
                        ROW FORMAT DELIMITED
                        FIELDS TERMINATED BY ' '
                        STORED AS TEXTFILE
                    """)
                    print("   ✓ Hive表attendance_data已创建")
                else:
                    print("   ✓ Hive表attendance_data已存在")
            except Exception as e:
                print(f"   ✗ 创建Hive表失败: {e}")

            return True

        except Exception as e:
            print(f"数据流前置步骤检查失败: {e}")
            return False

    def load_data_from_hive(self):
        """严格按照数据流：Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL"""
        try:
            # 确保数据流前置步骤已完成
            if not self.ensure_data_flow_setup():
                print("数据流前置步骤失败")
                from pyspark.sql.types import StructType, StructField, StringType, IntegerType
                schema = StructType([
                    StructField("class_number", StringType(), True),
                    StructField("student_name", StringType(), True),
                    StructField("course_name", StringType(), True),
                    StructField("student_id", StringType(), True),
                    StructField("score", IntegerType(), True),
                    StructField("attendance_status", StringType(), True)
                ])
                return self.spark.createDataFrame([], schema)

            # 启用Hive支持
            self.spark.sql("USE attendance")

            print("正在从Hive数据仓库读取数据 (严格按照数据流)...")
            print("数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL")

            # 从Hive表读取数据 - 这些数据是通过完整数据流到达的
            df = self.spark.sql("""
                SELECT class_number, student_name, course_name,
                       student_id, score, attendance_status
                FROM attendance_data
            """)

            # 检查数据是否存在
            count = df.count()
            if count > 0:
                print(f"从Hive读取到 {count} 条记录")
                return df
            else:
                print("Hive表中暂无数据")
                # 返回空DataFrame
                from pyspark.sql.types import StructType, StructField, StringType, IntegerType
                schema = StructType([
                    StructField("class_number", StringType(), True),
                    StructField("student_name", StringType(), True),
                    StructField("course_name", StringType(), True),
                    StructField("student_id", StringType(), True),
                    StructField("score", IntegerType(), True),
                    StructField("attendance_status", StringType(), True)
                ])
                return self.spark.createDataFrame([], schema)

        except Exception as e:
            print(f"从Hive加载数据失败: {e}")

            # 返回空DataFrame
            from pyspark.sql.types import StructType, StructField, StringType, IntegerType
            schema = StructType([
                StructField("class_number", StringType(), True),
                StructField("student_name", StringType(), True),
                StructField("course_name", StringType(), True),
                StructField("student_id", StringType(), True),
                StructField("score", IntegerType(), True),
                StructField("attendance_status", StringType(), True)
            ])
            return self.spark.createDataFrame([], schema)

    def monitor_hive_and_process_sql(self):
        """监控Hive表变化并处理 - 严格按照数据流"""
        import time
        import threading

        def monitor_loop():
            last_count = 0
            while True:
                try:
                    # 检查Hive表中的数据
                    current_df = self.load_data_from_hive()
                    current_count = current_df.count()

                    # 如果有新数据，进行处理
                    if current_count > last_count:
                        print(f"检测到新数据: {current_count - last_count} 条")
                        self._process_sql_data(current_df)
                        last_count = current_count

                    # 每5秒检查一次
                    time.sleep(5)

                except Exception as e:
                    print(f"监控Hive表失败: {e}")
                    time.sleep(5)

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread

    def _process_sql_data(self, df):
        """使用Spark SQL处理数据"""
        try:
            # 注册临时视图
            df.createOrReplaceTempView("attendance_data")

            # 使用Spark SQL进行统计
            sql_query = """
            SELECT
                course_name,
                SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
                COUNT(*) as total_count,
                ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate
            FROM attendance_data
            GROUP BY course_name
            ORDER BY attendance_rate DESC, present_count DESC
            """

            result_df = self.spark.sql(sql_query)

            # 收集结果
            results = result_df.collect()

            # 保存到MySQL
            if results:
                self._save_sql_results_to_mysql(results)

        except Exception as e:
            print(f"SQL处理数据失败: {e}")
    
    def process_course_attendance_sql(self):
        """使用Spark SQL统计各个课程号的出勤和缺勤数量"""
        print("=== 使用Spark SQL统计各个课程的出勤和缺勤数量 ===")
        
        try:
            # 加载数据 - 严格按照数据流
            df = self.load_data_from_hive()

            # 处理数据
            self._process_sql_data(df)

            print("Spark SQL处理完成")
            return True

        except Exception as e:
            print(f"Spark SQL处理出错: {e}")
            return False

    def start_offline_sql_processing(self):
        """启动离线SQL处理 - 严格按照数据流"""
        print("=== 启动离线SQL处理 ===")
        print("数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL → MySQL")

        # 启动Hive监控
        monitor_thread = self.monitor_hive_and_process_sql()

        if monitor_thread:
            print("SQL处理已启动，正在监控Hive数据变化...")
            print("数据流严格按照: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL")
            return monitor_thread
        else:
            print("启动SQL处理失败")
            return None

    def demonstrate_spark_sql_operations(self):
        """演示各种Spark SQL操作"""
        print("=== Spark SQL操作演示 ===")
        
        try:
            # 加载数据
            df = self.load_data_from_hive()
            df.createOrReplaceTempView("attendance_data")
            
            # 1. 基本统计
            print("1. 基本统计信息:")
            basic_stats = self.spark.sql("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT student_id) as unique_students,
                    COUNT(DISTINCT course_name) as unique_courses,
                    COUNT(DISTINCT class_number) as unique_classes
                FROM attendance_data
            """)
            basic_stats.show()
            
            # 2. 按班级统计
            print("2. 按班级统计:")
            class_stats = self.spark.sql("""
                SELECT 
                    class_number,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count
                FROM attendance_data
                GROUP BY class_number
                ORDER BY class_number
            """)
            class_stats.show()
            
            # 3. 按学生统计
            print("3. 按学生统计:")
            student_stats = self.spark.sql("""
                SELECT 
                    student_name,
                    student_id,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
                    ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate
                FROM attendance_data
                GROUP BY student_name, student_id
                ORDER BY attendance_rate DESC
            """)
            student_stats.show()
            
            # 4. 课程出勤率排名
            print("4. 课程出勤率排名:")
            course_ranking = self.spark.sql("""
                SELECT 
                    course_name,
                    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
                    ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate,
                    ROW_NUMBER() OVER (ORDER BY SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) DESC) as rank_order
                FROM attendance_data
                GROUP BY course_name
                ORDER BY attendance_rate DESC
            """)
            course_ranking.show()
            
        except Exception as e:
            print(f"Spark SQL演示出错: {e}")
    
    def _save_sql_results_to_mysql(self, results):
        """保存Spark SQL结果到MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 清空旧数据
            cursor.execute("DELETE FROM course_attendance_sql")
            
            # 插入新数据
            for row in results:
                sql = """
                INSERT INTO course_attendance_sql 
                (course_name, present_count, absent_count, total_count, attendance_rate, update_time)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    row['course_name'],
                    int(row['present_count']),
                    int(row['absent_count']),
                    int(row['total_count']),
                    float(row['attendance_rate']),
                    datetime.now()
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"成功保存 {len(results)} 条Spark SQL结果到MySQL")
            
        except Exception as e:
            print(f"保存Spark SQL结果到MySQL失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        self.spark.stop()

def main():
    """主函数"""
    processor = SparkSQLProcessor()

    try:
        # 启动离线SQL处理
        monitor_thread = processor.start_offline_sql_processing()

        if monitor_thread:
            print("\n离线SQL处理正在运行...")
            print("数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL → MySQL")
            print("按 Ctrl+C 停止")

            # 保持程序运行
            import time
            while True:
                time.sleep(10)
                print("SQL处理运行中... (监控Hive数据变化)")

    except KeyboardInterrupt:
        print("\n停止SQL处理...")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
    finally:
        processor.cleanup()

if __name__ == "__main__":
    main()
