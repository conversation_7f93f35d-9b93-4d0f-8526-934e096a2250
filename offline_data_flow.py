#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线数据流处理器
严格按照要求：Kafka(B组)消费 -> Flume采集(到HDFS) -> Sqoop抽取(到Hive) -> Spark SQL处理 -> MySQL
"""

import time
import subprocess
import os
import mysql.connector
from datetime import datetime
from pyspark.sql import SparkSession

class OfflineDataFlowProcessor:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'attendance_db'
        }
        
        # 初始化Spark Session with Hive support
        self.spark = SparkSession.builder \
            .appName("OfflineDataFlowProcessor") \
            .config("spark.sql.warehouse.dir", "/user/hive/warehouse") \
            .enableHiveSupport() \
            .getOrCreate()
        
        self.spark.sparkContext.setLogLevel("WARN")
        
    def check_hdfs_data(self):
        """检查HDFS中是否有Flume采集的数据"""
        try:
            result = subprocess.run([
                '/export/server/hadoop/bin/hdfs', 'dfs', '-ls', '/home/<USER>/attendance'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                print("✓ HDFS中发现Flume采集的数据")
                return True
            else:
                print("✗ HDFS中暂无Flume数据")
                return False
        except Exception as e:
            print(f"✗ 检查HDFS数据失败: {e}")
            return False
    
    def setup_hive_database(self):
        """设置Hive数据库和表"""
        try:
            print("设置Hive数据库...")

            # 创建数据库
            self.spark.sql("CREATE DATABASE IF NOT EXISTS attendance")
            self.spark.sql("USE attendance")

            # 删除旧表（如果存在）
            self.spark.sql("DROP TABLE IF EXISTS attendance_raw")

            # 创建分区外部表指向HDFS数据
            self.spark.sql("""
                CREATE TABLE IF NOT EXISTS attendance_raw (
                    class_number STRING,
                    student_name STRING,
                    course_name STRING,
                    student_id STRING,
                    score INT,
                    attendance_status STRING
                )
                PARTITIONED BY (year STRING, month STRING, day STRING)
                ROW FORMAT DELIMITED
                FIELDS TERMINATED BY ' '
                STORED AS TEXTFILE
                LOCATION '/home/<USER>/attendance'
            """)

            print("✓ Hive数据库和表设置完成")
            return True
        except Exception as e:
            print(f"✗ 设置Hive失败: {e}")
            return False
    
    def run_sqoop_import(self):
        """运行Sqoop从HDFS导入到Hive"""
        try:
            print("执行Sqoop数据导入...")

            self.spark.sql("USE attendance")

            # 添加分区
            self.spark.sql("""
                ALTER TABLE attendance_raw ADD IF NOT EXISTS
                PARTITION (year='2025', month='06', day='13')
                LOCATION '/home/<USER>/attendance/2025/06/13'
            """)

            # 刷新Hive表分区
            self.spark.sql("MSCK REPAIR TABLE attendance_raw")

            print("✓ Sqoop导入完成")
            return True
        except Exception as e:
            print(f"✗ Sqoop导入失败: {e}")
            return False
    
    def process_with_spark_rdd(self):
        """221) 用spark core rdd统计各个班级号所有学生的出勤和缺勤的数量"""
        try:
            print("执行Spark RDD处理 - 班级统计...")
            
            # 从Hive读取数据
            self.spark.sql("USE attendance")
            df = self.spark.sql("SELECT * FROM attendance_raw")
            
            if df.count() == 0:
                print("Hive表中暂无数据")
                return False
            
            # 转换为RDD进行处理
            rdd = df.rdd
            
            # 统计各班级出勤缺勤数量
            class_stats = rdd.map(lambda row: (row.class_number, row.attendance_status)) \
                            .filter(lambda x: x[0] is not None and x[1] is not None) \
                            .map(lambda x: ((x[0], x[1]), 1)) \
                            .reduceByKey(lambda a, b: a + b) \
                            .collect()
            
            # 整理数据
            class_summary = {}
            for (class_num, status), count in class_stats:
                if class_num not in class_summary:
                    class_summary[class_num] = {'present': 0, 'absent': 0}
                
                if status == 'A':
                    class_summary[class_num]['present'] = count
                elif status == '1':
                    class_summary[class_num]['absent'] = count
            
            # 保存到MySQL
            self.save_rdd_results_to_mysql(class_summary)
            
            print(f"✓ RDD处理完成，处理了{len(class_summary)}个班级")
            return True
            
        except Exception as e:
            print(f"✗ Spark RDD处理失败: {e}")
            return False
    
    def process_with_spark_sql(self):
        """222) 使用spark sql统计各个课程号的出勤和缺勤数量"""
        try:
            print("执行Spark SQL处理 - 课程统计...")
            
            self.spark.sql("USE attendance")
            
            # 使用Spark SQL统计课程出勤情况
            course_stats_df = self.spark.sql("""
                SELECT 
                    course_name,
                    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
                    COUNT(*) as total_count,
                    ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate
                FROM attendance_raw
                WHERE course_name IS NOT NULL
                GROUP BY course_name
                ORDER BY attendance_rate DESC
            """)
            
            if course_stats_df.count() == 0:
                print("无课程数据可处理")
                return False
            
            # 保存到MySQL
            self.save_sql_results_to_mysql(course_stats_df)
            
            print(f"✓ Spark SQL处理完成，处理了{course_stats_df.count()}门课程")
            return True
            
        except Exception as e:
            print(f"✗ Spark SQL处理失败: {e}")
            return False
    
    def save_rdd_results_to_mysql(self, class_summary):
        """保存RDD结果到MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 清空旧数据
            cursor.execute("DELETE FROM class_attendance_rdd")
            
            for class_number, stats in class_summary.items():
                sql = """
                INSERT INTO class_attendance_rdd
                (class_number, present_count, absent_count, update_time)
                VALUES (%s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    class_number,
                    stats['present'],
                    stats['absent'],
                    datetime.now()
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"✓ RDD结果已保存到MySQL")
            
        except Exception as e:
            print(f"✗ 保存RDD结果失败: {e}")
    
    def save_sql_results_to_mysql(self, course_stats_df):
        """保存SQL结果到MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 清空旧数据
            cursor.execute("DELETE FROM course_attendance_sql")
            
            # 收集数据并插入
            for row in course_stats_df.collect():
                sql = """
                INSERT INTO course_attendance_sql
                (course_name, present_count, absent_count, total_count, attendance_rate, update_time)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    row.course_name,
                    int(row.present_count),
                    int(row.absent_count),
                    int(row.total_count),
                    float(row.attendance_rate),
                    datetime.now()
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"✓ SQL结果已保存到MySQL")
            
        except Exception as e:
            print(f"✗ 保存SQL结果失败: {e}")

    def process_offline_recommendations(self):
        """241) 离线推荐：展示缺席最多的前N的名单"""
        try:
            print("执行离线推荐处理 - 缺席最多的学生排名...")

            self.spark.sql("USE attendance")

            # 使用Spark SQL统计学生缺席情况
            absence_ranking_df = self.spark.sql("""
                SELECT
                    student_id,
                    student_name,
                    class_number,
                    COUNT(*) as total_records,
                    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absence_count,
                    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
                    ROUND(SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as absence_rate
                FROM attendance_raw
                WHERE student_id IS NOT NULL AND student_name IS NOT NULL
                GROUP BY student_id, student_name, class_number
                HAVING COUNT(*) >= 3
                ORDER BY absence_count DESC, absence_rate DESC
                LIMIT 20
            """)

            if absence_ranking_df.count() == 0:
                print("无学生缺席数据可处理")
                return False

            # 保存到MySQL
            self.save_offline_recommendations_to_mysql(absence_ranking_df)

            print(f"✓ 离线推荐处理完成，排名了{absence_ranking_df.count()}名学生")
            return True

        except Exception as e:
            print(f"✗ 离线推荐处理失败: {e}")
            return False

    def save_offline_recommendations_to_mysql(self, absence_ranking_df):
        """保存离线推荐结果到MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()

            # 清空旧数据
            cursor.execute("DELETE FROM student_absence_ranking")

            # 收集数据并插入
            rank_order = 1
            for row in absence_ranking_df.collect():
                sql = """
                INSERT INTO student_absence_ranking
                (student_id, student_name, total_absence, absence_rate, rank_order, update_time)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    row.student_id,
                    row.student_name,
                    int(row.absence_count),
                    float(row.absence_rate),
                    rank_order,
                    datetime.now()
                ))
                rank_order += 1

            conn.commit()
            cursor.close()
            conn.close()

            print(f"✓ 离线推荐结果已保存到MySQL")

        except Exception as e:
            print(f"✗ 保存离线推荐结果失败: {e}")

    def run_offline_processing_cycle(self):
        """运行一次完整的离线处理周期"""
        print(f"\n=== 离线数据流处理开始 [{datetime.now()}] ===")
        print("数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL")
        
        # 1. 检查HDFS数据
        if not self.check_hdfs_data():
            print("等待Flume采集数据到HDFS...")
            return False
        
        # 2. 设置Hive
        if not self.setup_hive_database():
            return False
        
        # 3. Sqoop导入
        if not self.run_sqoop_import():
            return False
        
        # 4. Spark RDD处理
        if not self.process_with_spark_rdd():
            return False
        
        # 5. Spark SQL处理
        if not self.process_with_spark_sql():
            return False

        # 6. 离线推荐处理
        if not self.process_offline_recommendations():
            return False

        print("=== 离线数据流处理完成 ===\n")
        return True
    
    def start_monitoring(self):
        """启动监控，每5秒检查一次数据流"""
        print("启动离线数据流监控...")
        print("每5秒检查一次数据更新")
        
        while True:
            try:
                self.run_offline_processing_cycle()
                time.sleep(5)  # 每5秒处理一次
                
            except KeyboardInterrupt:
                print("\n停止离线数据流处理")
                break
            except Exception as e:
                print(f"处理出错: {e}")
                time.sleep(5)

def main():
    processor = OfflineDataFlowProcessor()
    processor.start_monitoring()

if __name__ == "__main__":
    main()
