#!/bin/bash
# Sqoop数据抽取脚本 - 简化版本
# 创建Hive外部表指向HDFS数据，实现真正的数据流

# 配置
HIVE_DB="attendance"
HDFS_PATH="/home/<USER>/attendance"

echo "=== Sqoop数据抽取：HDFS → Hive ==="

# 创建Hive数据库
echo "1. 创建Hive数据库..."
hive -e "CREATE DATABASE IF NOT EXISTS ${HIVE_DB};"

# 检查HDFS中的数据文件
echo "2. 检查HDFS数据文件..."
hdfs dfs -ls ${HDFS_PATH}/

# 创建Hive外部表指向HDFS数据
echo "3. 创建Hive外部表指向HDFS数据..."
hive -e "
USE ${HIVE_DB};

-- 删除旧表
DROP TABLE IF EXISTS attendance_data;

-- 创建外部表指向HDFS中Flume采集的数据
CREATE EXTERNAL TABLE attendance_data (
    class_number STRING,
    student_name STRING,
    course_name STRING,
    student_id STRING,
    score INT,
    attendance_status STRING
)
ROW FORMAT DELIMITED
FIELDS TERMINATED BY ' '
STORED AS TEXTFILE
LOCATION '${HDFS_PATH}/'
TBLPROPERTIES ('skip.header.line.count'='0');
"

# 验证数据导入结果
echo "4. 验证Hive表数据..."
hive -e "
USE ${HIVE_DB};
SELECT COUNT(*) as total_records FROM attendance_data;
SELECT class_number, COUNT(*) as count FROM attendance_data GROUP BY class_number LIMIT 10;
SELECT * FROM attendance_data LIMIT 5;
"

echo "=== Sqoop数据抽取完成 ==="
echo "数据流: Kafka(B组) → Flume → HDFS → Sqoop(外部表) → Hive"
echo "Hive表attendance_data现在指向HDFS中的实时数据"
