# Flume配置文件 - Kafka到HDFS数据采集
# 定时采集kafka数据写入到hdfs的/home/<USER>

# Agent配置
agent.sources = kafka-source
agent.sinks = hdfs-sink
agent.channels = memory-channel

# Source配置 - Kafka Source
agent.sources.kafka-source.type = org.apache.flume.source.kafka.KafkaSource
agent.sources.kafka-source.kafka.bootstrap.servers = localhost:9092
agent.sources.kafka-source.kafka.topics = attendance
agent.sources.kafka-source.kafka.consumer.group.id = offline_group_b
agent.sources.kafka-source.channels = memory-channel

# Channel配置 - Memory Channel
agent.channels.memory-channel.type = memory
agent.channels.memory-channel.capacity = 10000
agent.channels.memory-channel.transactionCapacity = 1000

# Sink配置 - HDFS Sink
agent.sinks.hdfs-sink.type = hdfs
agent.sinks.hdfs-sink.hdfs.path = /home/<USER>/attendance/%Y/%m/%d
agent.sinks.hdfs-sink.hdfs.filePrefix = attendance-data
agent.sinks.hdfs-sink.hdfs.fileSuffix = .txt
agent.sinks.hdfs-sink.hdfs.rollInterval = 300
agent.sinks.hdfs-sink.hdfs.rollSize = 1048576
agent.sinks.hdfs-sink.hdfs.rollCount = 0
agent.sinks.hdfs-sink.hdfs.fileType = DataStream
agent.sinks.hdfs-sink.hdfs.useLocalTimeStamp = true
agent.sinks.hdfs-sink.channel = memory-channel
