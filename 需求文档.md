代码检查官一条一条检查是否满足，特别是数据流的实现必须要满足 最后只能是Mysql 追加刷新给到前端。不允许模拟 一切要真实，一切数据流要按要求实现。最后启动检查下数据是否都定时更新到mysql。
你要运行真实检查。并且删除无关代码内容。你先获取当前时间然后启动服务检查每一个流是否有更新到mysql。所有界面的流都需要检查。当前时间执行后 有没有更新。没有更新立马修复

1) 注册和登录页面，用户信息存储于mysql中（表也是没有创建的 用户名 root 密码 123456）
2) 用户登录后，首页使用echarts展示(只要Kakfa 推送了数据所有内容都要更新一遍)
   21) 实时界面html（Kafka生产者S (每5秒推送)   ->  Kafka(A组)消费 ->  Spark流处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）：
      211) 用spark streaming创建消费者读取相应主题的数据
      212) 用spark streaming实时统计所有学生出勤和缺勤数量总和
      213) 用spark streaming实时统计各个班级号各自的出勤和缺勤数量
      214) 用spark streaming实时统计各个课程的数量
   22) 离线界面html（Kafka生产者S (每5秒推送)   ->  Kafka(B组)消费 ->  Flume采集(到HDFS) ->Sqoop抽取(到Hive数据库attendance)  ->  Spark SQL处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）:
      221) 用spark core rdd统计各个班级号所有学生的出勤和缺勤的数量
      222) 使用spark sql统计各个课程号的出勤和缺勤数量
   23) 实时推荐界面html（Kafka生产者S (每5秒推送)   ->  Kafka(A组)消费 ->  Spark流处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）：
      231) 个性化推荐：展示个性化推荐缺勤前6的学生名单。基于CF6个。(基于协同过滤推荐的名单)
            2311) 协同过滤推荐 （结果需要再前端展示推荐内容）
                  a. 建立用户-课程的关系矩阵
                  b. 建立课程-课程的关系矩阵
                  c. 基于User-Base CF生成每个用户的推荐列表
   24) 离线推荐界面html（Kafka生产者S (每5秒推送)   ->  Kafka(B组)消费 ->  Flume采集(到HDFS) ->Sqoop抽取(到Hive数据库attendance)  ->  Spark SQL处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）:
      241) 离线推荐：展示缺席最多的前N的名单
   25) 通过搜索展示目标学生功能html，预测可能缺席的top3的名单

# 学生出勤管理系统需求文档

## 数据格式定义
6个字段：分别是班级号，学生名字，课程名，学号，分数(出勤100，缺勤0)，是否出勤:1是缺勤A出勤。分隔符可以用空格进行拼接。

## 1. Kafka数据生产 (Kafka data production)

这个模块主要用来模拟在实时的业务系统中，用户数据是实时生产的,并推送到kafka相应的主题中。

1) 使用Python代码实现producer，并每隔5s推送以下数据给kafka的attendance主题需要推送的数据格式，请使用随机数+数组的形式进行随机产生

2) 使用flume定时采集相应kafka的数据写入到hdfs的/home/<USER>

3) 使用sqoop定时将Spark data processing所有处理的所有需求数据抽取到中央仓库hive，建立相应的数据库名字attendance


展示学生信息
1) 注册和登录页面，用户信息存储于mysql中（表也是没有创建的 用户名 root 密码 123456）
2) 用户登录后，首页使用echarts展示(只要Kakfa 推送了数据所有内容都要更新一遍)
   21) 实时界面html（Kafka生产者S (每5秒推送)   ->  Kafka(A组)消费 ->  Spark流处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）：
      211) 用spark streaming创建消费者读取相应主题的数据
      212) 用spark streaming实时统计所有学生出勤和缺勤数量总和
      213) 用spark streaming实时统计各个班级号各自的出勤和缺勤数量
      214) 用spark streaming实时统计各个课程的数量
   22) 离线界面html（Kafka生产者S (每5秒推送)   ->  Kafka(B组)消费 ->  Flume采集(到HDFS) ->Sqoop抽取(到Hive数据库attendance)  ->  Spark SQL处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）:
      221) 用spark core rdd统计各个班级号所有学生的出勤和缺勤的数量
      222) 使用spark sql统计各个课程号的出勤和缺勤数量
   23) 实时推荐界面html（Kafka生产者S (每5秒推送)   ->  Kafka(A组)消费 ->  Spark流处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）：
      231) 个性化推荐：展示个性化推荐缺勤前6的学生名单。基于CF6个。(基于协同过滤推荐的名单)
            2311) 协同过滤推荐 （结果需要再前端展示推荐内容）
                  a. 建立用户-课程的关系矩阵
                  b. 建立课程-课程的关系矩阵
                  c. 基于User-Base CF生成每个用户的推荐列表
   24) 离线推荐界面html（Kafka生产者S (每5秒推送)   ->  Kafka(B组)消费 ->  Flume采集(到HDFS) ->Sqoop抽取(到Hive数据库attendance)  ->  Spark SQL处理 -> MySQL数据库  (结果存储attendance_db)  ->  Web可视化）:
      241) 离线推荐：展示缺席最多的前N的名单
   25) 通过搜索展示目标学生功能html，预测可能缺席的top3的名单

## 补充说明
6个字段：分别是班级号，学生名字，课程名，学号，分数(出勤100，缺勤0)，是否出勤:1是缺勤A出勤。分隔符可以用空格进行拼接

程序只需要保存关键代码，无效代码请给我删除。
MD文件只保存：/root/job14/需求文档.md 不用改
