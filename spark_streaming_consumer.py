#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spark Streaming消费者
实现各种统计需求
"""

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.streaming import StreamingContext
from pyspark import SparkContext, SparkConf
import mysql.connector
from datetime import datetime

class SparkAttendanceProcessor:
    def __init__(self):
        # 初始化Spark Session
        self.spark = SparkSession.builder \
            .appName("AttendanceStreamingProcessor") \
            .config("spark.sql.adaptive.enabled", "true") \
            .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
            .getOrCreate()
        
        self.spark.sparkContext.setLogLevel("WARN")
        
        # MySQL连接配置
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',  # 根据需求文档配置
            'database': 'attendance_db'
        }
        
    def create_kafka_stream(self):
        """创建Kafka流 - A组消费者用于实时处理"""
        df = self.spark \
            .readStream \
            .format("kafka") \
            .option("kafka.bootstrap.servers", "localhost:9092") \
            .option("subscribe", "attendance") \
            .option("kafka.group.id", "realtime_group_a") \
            .option("startingOffsets", "latest") \
            .load()
        
        return df
    
    def parse_attendance_data(self, df):
        """解析出勤数据"""
        # 定义数据schema
        schema = StructType([
            StructField("class_number", StringType(), True),
            StructField("student_name", StringType(), True),
            StructField("course_name", StringType(), True),
            StructField("student_id", StringType(), True),
            StructField("score", IntegerType(), True),
            StructField("attendance_status", StringType(), True)
        ])
        
        # 解析Kafka消息
        parsed_df = df.select(
            col("value").cast("string").alias("raw_data"),
            col("timestamp")
        ).select(
            split(col("raw_data"), " ").alias("data_array"),
            col("timestamp")
        ).select(
            col("data_array")[0].alias("class_number"),
            col("data_array")[1].alias("student_name"),
            col("data_array")[2].alias("course_name"),
            col("data_array")[3].alias("student_id"),
            col("data_array")[4].cast("int").alias("score"),
            col("data_array")[5].alias("attendance_status"),
            col("timestamp")
        )
        
        return parsed_df
    
    def save_to_mysql(self, df, table_name):
        """保存数据到MySQL"""
        def write_to_mysql(batch_df, batch_id):
            try:
                # 收集数据到驱动程序
                rows = batch_df.collect()

                if rows:
                    # 连接MySQL
                    conn = mysql.connector.connect(**self.mysql_config)
                    cursor = conn.cursor()

                    # 根据表名执行不同的插入逻辑
                    if table_name == "attendance_summary":
                        for row in rows:
                            # 直接使用Spark Row对象，避免Pandas转换问题
                            total_present = int(row['total_present']) if row['total_present'] is not None else 0
                            total_absent = int(row['total_absent']) if row['total_absent'] is not None else 0

                            sql = """
                            INSERT INTO attendance_summary
                            (total_present, total_absent, update_time)
                            VALUES (%s, %s, %s)
                            ON DUPLICATE KEY UPDATE
                            total_present = VALUES(total_present),
                            total_absent = VALUES(total_absent),
                            update_time = VALUES(update_time)
                            """
                            cursor.execute(sql, (
                                total_present,
                                total_absent,
                                datetime.now()
                            ))
                    
                    elif table_name == "class_attendance":
                        for row in rows:
                            # 直接使用Spark Row对象
                            present_count = int(row['present_count']) if row['present_count'] is not None else 0
                            absent_count = int(row['absent_count']) if row['absent_count'] is not None else 0

                            sql = """
                            INSERT INTO class_attendance
                            (class_number, present_count, absent_count, update_time)
                            VALUES (%s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE
                            present_count = VALUES(present_count),
                            absent_count = VALUES(absent_count),
                            update_time = VALUES(update_time)
                            """
                            cursor.execute(sql, (
                                str(row['class_number']),
                                present_count,
                                absent_count,
                                datetime.now()
                            ))
                    
                    elif table_name == "course_attendance":
                        for row in rows:
                            # 直接使用Spark Row对象
                            present_count = int(row['present_count']) if row['present_count'] is not None else 0
                            absent_count = int(row['absent_count']) if row['absent_count'] is not None else 0

                            sql = """
                            INSERT INTO course_attendance
                            (course_name, present_count, absent_count, update_time)
                            VALUES (%s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE
                            present_count = VALUES(present_count),
                            absent_count = VALUES(absent_count),
                            update_time = VALUES(update_time)
                            """
                            cursor.execute(sql, (
                                str(row['course_name']),
                                present_count,
                                absent_count,
                                datetime.now()
                            ))

                    elif table_name == "raw_attendance_data":
                        for row in rows:
                            # 直接使用Spark Row对象
                            score = int(row['score']) if row['score'] is not None else 0

                            sql = """
                            INSERT INTO raw_attendance_data
                            (class_number, student_name, course_name, student_id, score, attendance_status, record_time)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """
                            cursor.execute(sql, (
                                str(row['class_number']),
                                str(row['student_name']),
                                str(row['course_name']),
                                str(row['student_id']),
                                score,
                                str(row['attendance_status']),
                                datetime.now()
                            ))
                    
                    conn.commit()
                    cursor.close()
                    conn.close()
                    
                    print(f"[{datetime.now()}] 成功写入{len(rows)}条记录到{table_name}")
                    
            except Exception as e:
                print(f"写入MySQL失败: {e}")
        
        return write_to_mysql
    
    def process_total_attendance(self, parsed_df):
        """需求2: 实时统计所有学生出勤和缺勤数量总和"""
        total_stats = parsed_df.groupBy().agg(
            sum(when(col("attendance_status") == "A", 1).otherwise(0)).alias("total_present"),
            sum(when(col("attendance_status") == "1", 1).otherwise(0)).alias("total_absent")
        )
        
        # 写入MySQL
        query = total_stats.writeStream \
            .outputMode("complete") \
            .foreachBatch(self.save_to_mysql(total_stats, "attendance_summary")) \
            .trigger(processingTime='10 seconds') \
            .start()
        
        return query
    
    def process_class_attendance(self, parsed_df):
        """需求3: 实时统计各个班级号各自的出勤和缺勤数量"""
        class_stats = parsed_df.groupBy("class_number").agg(
            sum(when(col("attendance_status") == "A", 1).otherwise(0)).alias("present_count"),
            sum(when(col("attendance_status") == "1", 1).otherwise(0)).alias("absent_count")
        )
        
        # 写入MySQL
        query = class_stats.writeStream \
            .outputMode("complete") \
            .foreachBatch(self.save_to_mysql(class_stats, "class_attendance")) \
            .trigger(processingTime='10 seconds') \
            .start()
        
        return query
    
    def process_course_attendance(self, parsed_df):
        """需求4: 实时统计各个课程号的出勤和缺勤数量"""
        course_stats = parsed_df.groupBy("course_name").agg(
            sum(when(col("attendance_status") == "A", 1).otherwise(0)).alias("present_count"),
            sum(when(col("attendance_status") == "1", 1).otherwise(0)).alias("absent_count")
        )
        
        # 写入MySQL
        query = course_stats.writeStream \
            .outputMode("complete") \
            .foreachBatch(self.save_to_mysql(course_stats, "course_attendance")) \
            .trigger(processingTime='10 seconds') \
            .start()
        
        return query

    def save_raw_data(self, parsed_df):
        """保存原始数据到MySQL"""
        query = parsed_df.writeStream \
            .outputMode("append") \
            .foreachBatch(self.save_to_mysql(parsed_df, "raw_attendance_data")) \
            .trigger(processingTime='5 seconds') \
            .start()

        return query

    def start_streaming(self):
        """启动流处理"""
        print("启动Spark Streaming处理...")
        
        # 创建Kafka流
        kafka_df = self.create_kafka_stream()
        
        # 解析数据
        parsed_df = self.parse_attendance_data(kafka_df)
        
        # 启动各种统计任务
        query0 = self.save_raw_data(parsed_df)  # 保存原始数据
        query1 = self.process_total_attendance(parsed_df)
        query2 = self.process_class_attendance(parsed_df)
        query3 = self.process_course_attendance(parsed_df)
        
        try:
            # 等待所有查询完成
            query0.awaitTermination()
            query1.awaitTermination()
            query2.awaitTermination()
            query3.awaitTermination()
        except KeyboardInterrupt:
            print("停止流处理...")
            query0.stop()
            query1.stop()
            query2.stop()
            query3.stop()

def main():
    processor = SparkAttendanceProcessor()
    processor.start_streaming()

if __name__ == "__main__":
    main()
