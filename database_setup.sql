-- 学生出勤管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS attendance_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE attendance_db;

-- 1. 用户表（用于登录注册）
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    real_name VARCHAR(50),
    role ENUM('student', 'teacher', 'admin') DEFAULT 'student',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 原始出勤数据表
CREATE TABLE IF NOT EXISTS raw_attendance_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_number VARCHAR(20) NOT NULL,
    student_name VARCHAR(50) NOT NULL,
    course_name VARCHAR(100) NOT NULL,
    student_id VARCHAR(20) NOT NULL,
    score INT NOT NULL,
    attendance_status CHAR(1) NOT NULL COMMENT 'A=出勤, 1=缺勤',
    record_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_class_number (class_number),
    INDEX idx_student_id (student_id),
    INDEX idx_course_name (course_name),
    INDEX idx_attendance_status (attendance_status)
);

-- 3. 总体出勤统计表
CREATE TABLE IF NOT EXISTS attendance_summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    total_present INT DEFAULT 0,
    total_absent INT DEFAULT 0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 班级出勤统计表
CREATE TABLE IF NOT EXISTS class_attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_number VARCHAR(20) UNIQUE NOT NULL,
    present_count INT DEFAULT 0,
    absent_count INT DEFAULT 0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. 课程出勤统计表
CREATE TABLE IF NOT EXISTS course_attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100) UNIQUE NOT NULL,
    present_count INT DEFAULT 0,
    absent_count INT DEFAULT 0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 6. RDD处理结果表
CREATE TABLE IF NOT EXISTS class_attendance_rdd (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_number VARCHAR(20) UNIQUE NOT NULL,
    present_count INT DEFAULT 0,
    absent_count INT DEFAULT 0,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 6.1. SQL处理结果表
CREATE TABLE IF NOT EXISTS course_attendance_sql (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100) UNIQUE NOT NULL,
    present_count INT DEFAULT 0,
    absent_count INT DEFAULT 0,
    total_count INT DEFAULT 0,
    attendance_rate DECIMAL(5,2) DEFAULT 0.00,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 7. 用户推荐表
CREATE TABLE IF NOT EXISTS user_recommendations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(20) NOT NULL,
    course_name VARCHAR(100) NOT NULL,
    recommendation_score DECIMAL(10,6) NOT NULL,
    rank_order INT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_rank_order (rank_order)
);

-- 8. 学生缺勤排名表（用于离线推荐）
CREATE TABLE IF NOT EXISTS student_absence_ranking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL,
    student_name VARCHAR(50) NOT NULL,
    total_absence INT DEFAULT 0,
    absence_rate DECIMAL(5,2) DEFAULT 0.00,
    rank_order INT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rank_order (rank_order)
);

-- 9. 课程出勤TOP5表
CREATE TABLE IF NOT EXISTS course_attendance_top5 (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100) NOT NULL,
    present_count INT DEFAULT 0,
    absent_count INT DEFAULT 0,
    attendance_rate DECIMAL(5,2) DEFAULT 0.00,
    rank_order INT,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rank_order (rank_order)
);

-- 10. 学生预测缺勤表
CREATE TABLE IF NOT EXISTS student_absence_prediction (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL,
    student_name VARCHAR(50) NOT NULL,
    predicted_course VARCHAR(100) NOT NULL,
    prediction_score DECIMAL(10,6) NOT NULL,
    rank_order INT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_student_id (student_id),
    INDEX idx_rank_order (rank_order)
);

-- 插入示例用户数据
INSERT INTO users (username, password, email, real_name, role) VALUES
('admin', 'admin123', '<EMAIL>', '管理员', 'admin'),
('teacher1', 'teacher123', '<EMAIL>', '张老师', 'teacher'),
('student1', 'student123', '<EMAIL>', '张三', 'student'),
('student2', 'student123', '<EMAIL>', '李四', 'student')
ON DUPLICATE KEY UPDATE username=username;

-- 插入示例出勤数据
INSERT INTO raw_attendance_data (class_number, student_name, course_name, student_id, score, attendance_status) VALUES
('CS2021', '张三', '高等数学', '2021CS001', 100, 'A'),
('CS2021', '张三', '线性代数', '2021CS001', 0, '1'),
('CS2021', '李四', '高等数学', '2021CS002', 100, 'A'),
('CS2021', '李四', '数据结构', '2021CS002', 0, '1'),
('CS2022', '王五', '概率论', '2022CS001', 100, 'A'),
('CS2022', '王五', '算法设计', '2022CS001', 100, 'A'),
('EE2021', '赵六', '电路分析', '2021EE001', 0, '1'),
('EE2021', '赵六', '信号处理', '2021EE001', 100, 'A')
ON DUPLICATE KEY UPDATE id=id;

-- 初始化统计数据
INSERT INTO attendance_summary (total_present, total_absent) VALUES (5, 3)
ON DUPLICATE KEY UPDATE total_present=5, total_absent=3;

INSERT INTO class_attendance (class_number, present_count, absent_count) VALUES
('CS2021', 2, 2),
('CS2022', 2, 0),
('EE2021', 1, 1)
ON DUPLICATE KEY UPDATE 
present_count=VALUES(present_count), 
absent_count=VALUES(absent_count);

INSERT INTO course_attendance (course_name, present_count, absent_count) VALUES
('高等数学', 2, 0),
('线性代数', 0, 1),
('数据结构', 0, 1),
('概率论', 1, 0),
('算法设计', 1, 0),
('电路分析', 0, 1),
('信号处理', 1, 0)
ON DUPLICATE KEY UPDATE 
present_count=VALUES(present_count), 
absent_count=VALUES(absent_count);

-- 创建视图：学生出勤统计视图
CREATE OR REPLACE VIEW student_attendance_stats AS
SELECT 
    student_id,
    student_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
    ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate
FROM raw_attendance_data
GROUP BY student_id, student_name;

-- 创建视图：课程出勤统计视图
CREATE OR REPLACE VIEW course_attendance_stats AS
SELECT 
    course_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) as present_count,
    SUM(CASE WHEN attendance_status = '1' THEN 1 ELSE 0 END) as absent_count,
    ROUND(SUM(CASE WHEN attendance_status = 'A' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as attendance_rate
FROM raw_attendance_data
GROUP BY course_name;

-- 初始化学生缺勤排名数据
INSERT INTO student_absence_ranking (student_id, student_name, total_absence, absence_rate, rank_order)
SELECT
    student_id,
    student_name,
    absent_count,
    ROUND((100.0 - attendance_rate), 2) as absence_rate,
    ROW_NUMBER() OVER (ORDER BY absent_count DESC, attendance_rate ASC) as rank_order
FROM student_attendance_stats
ORDER BY absent_count DESC, attendance_rate ASC
ON DUPLICATE KEY UPDATE
total_absence=VALUES(total_absence),
absence_rate=VALUES(absence_rate),
rank_order=VALUES(rank_order);

-- 初始化课程出勤TOP5数据
INSERT INTO course_attendance_top5 (course_name, present_count, absent_count, attendance_rate, rank_order)
SELECT
    course_name,
    present_count,
    absent_count,
    attendance_rate,
    ROW_NUMBER() OVER (ORDER BY attendance_rate DESC, present_count DESC) as rank_order
FROM course_attendance_stats
ORDER BY attendance_rate DESC, present_count DESC
LIMIT 5
ON DUPLICATE KEY UPDATE
present_count=VALUES(present_count),
absent_count=VALUES(absent_count),
attendance_rate=VALUES(attendance_rate),
rank_order=VALUES(rank_order);

-- 显示表结构
SHOW TABLES;
