
function map_chart(){
    var myChart = echarts.init(document.getElementById('id3'));
    var mapName = 'china'
    var geoCoordMap = {};

    /*获取地图数据*/
    myChart.showLoading();

    // 确保地图数据已经注册
    setTimeout(function() {
        var mapData = echarts.getMap(mapName);
        var mapFeatures = [];

        if (mapData && mapData.geoJson && mapData.geoJson.features) {
            mapFeatures = mapData.geoJson.features;
        }

        mapFeatures.forEach(function(v) {
            // 地区名称
            var name = v.properties.name;
            // 地区经纬度
            geoCoordMap[name] = v.properties.cp;
        });

        myChart.hideLoading();

        // 在获取到地图数据后再设置option
        setupChart();
    }, 100);

    function setupChart() {

    var convertData = function(data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
            var geoCoord = geoCoordMap[data[i].name];
            if (geoCoord) {
                res.push({
                    name: data[i].name,
                    value: geoCoord.concat(data[i].value),
                });
            }
        }
        return res;
    };
    option = {
    //			backgroundColor: '#013954',
        title: {
            text: "2022年各省份"+month_index+"月份平均温度和风速情况",
            textStyle: text_style,
            top: '5%',
            left: 'center',
        },
        tooltip:{
            show:true,
            valueFormatter:(value) => value + '°C',
        },
        visualMap: {
            show: true,
            left: '8%',
            top: 'bottom',
            seriesIndex: [1],
            pieces:[
                {gt:25,label:'25°C以上',color:'#FF3300'},
                {gt:20,lte:25,label:'20°C-25°C',color:'#FF9966'},
                {gt:10,lte:20,label:'10°C-20°C',color:'#66FF33'},
                {gt:0,lte:10,label:'0°C-10°C',color:'#66CCFF'},
                {gt:-10,lte:0,label:'-10°C-0°C',color:'#0066FF'},
                {lte:-10,label:'-10°C以下',color:'#000099'}
            ],
            textStyle:{
                color:'white',
             },
        },
        geo: {
            show: true,
            map: mapName,
            roam: false,
            itemStyle: {
                normal: {
                    areaColor: '#023677',
                    borderColor: '#1180c7',
                },
                emphasis: {
                    areaColor: '#4499d0',
                }
            }
        },
        series: [{
                name: '散点',
                type: 'scatter',
                coordinateSystem: 'geo',
                data: convertData(map_data2[month_index]),
                label: {
                    normal: {
                        formatter: '{b}',
                        position: 'right',
                        show: true,
                        fontSize:8,
                    },
                    emphasis: {
                        show: true,

                    }
                },
    //		            animation: false,
                itemStyle: {
                    normal: {
                        color: '#fff',

                    }
                }
            },
            {
                name:'本月温度',
                type: 'map',
                map: mapName,
                geoIndex: 0,
                aspectScale: 0.5, //长宽比
                showLegendSymbol: false, // 存在legend时显示
                label: {
                    normal: {
                        show: true
                    },
                    emphasis: {
                        show: false,
                        textStyle: {
                            color: '#fff'
                        }
                    }
                },
                roam: true,
                animation: false,
                data: map_data1[month_index]
            },
            {
                name: '散点',
                type: 'effectScatter',
    //		            animation: false,
                coordinateSystem: 'geo',
                data: convertData(map_data2[month_index]),
                symbolSize: function(val) {
                    return val[2]*5 ;
                },
                showEffectOn: 'render',
                rippleEffect: { //涟漪特效
                    period: 4, //动画时间，值越小速度越快
                    brushType: 'stroke', //波纹绘制方式 stroke, fill
                    scale: 4 //波纹圆环最大限制，值越大波纹越大
                },
                hoverAnimation: true,
                label: {
                    normal: {
                        formatter: '{b}',
                        position: 'left',
                        show: false
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'yellow',
                        shadowBlur: 10,
                        shadowColor: 'yellow'
                    }
                },
                zlevel: 1
            },

        ]
    };
    myChart.setOption(option);
    console.log(map_data1[month_index]);
    } // 结束setupChart函数
} // 结束map_chart函数

