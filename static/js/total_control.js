map_chart();
tree_chart();
word_chart();
bar_chart();
function month_index_charge(){
    var current_month = parseInt(month_index);
    if(current_month == 12){
        month_index = '01';
    }else{
        current_month++;
        month_index = current_month.toString().padStart(2, '0');
    };
//    console.log(map_data1[month_index]);
    map_chart();
    tree_chart();
    word_chart();
    bar_chart();
//    console.log(month_index);

};
setInterval(month_index_charge,time_interval);
timeline();
line_chart();

