/**
 * 学生出勤管理系统主要JavaScript文件
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '',
    REFRESH_INTERVAL: 30000, // 30秒刷新间隔
    CHART_COLORS: {
        primary: '#007bff',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    }
};

// 工具函数
const Utils = {
    /**
     * 格式化数字
     */
    formatNumber: function(num) {
        return num.toLocaleString();
    },

    /**
     * 格式化百分比
     */
    formatPercentage: function(num, decimals = 1) {
        return num.toFixed(decimals) + '%';
    },

    /**
     * 显示加载状态
     */
    showLoading: function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            `;
        }
    },

    /**
     * 显示错误信息
     */
    showError: function(elementId, message) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-circle"></i>
                    ${message}
                </div>
            `;
        }
    },

    /**
     * 显示空数据信息
     */
    showEmpty: function(elementId, message = '暂无数据') {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    },

    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 获取状态徽章类
     */
    getStatusBadgeClass: function(rate) {
        if (rate >= 80) return 'success';
        if (rate >= 60) return 'warning';
        return 'danger';
    },

    /**
     * 获取状态文本
     */
    getStatusText: function(rate) {
        if (rate >= 80) return '良好';
        if (rate >= 60) return '一般';
        return '较差';
    }
};

// API调用函数
const API = {
    /**
     * 通用API调用
     */
    call: async function(endpoint, options = {}) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + endpoint, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API调用失败:', error);
            throw error;
        }
    },

    /**
     * 获取出勤总和数据
     */
    getAttendanceSummary: function() {
        return this.call('/api/attendance_summary');
    },

    /**
     * 获取课程TOP5数据
     */
    getCourseTop5: function() {
        return this.call('/api/course_top5');
    },

    /**
     * 获取班级出勤数据
     */
    getClassAttendance: function() {
        return this.call('/api/class_attendance');
    },

    /**
     * 获取学生缺勤排名
     */
    getStudentAbsenceRanking: function(limit = 10) {
        return this.call(`/api/student_absence_ranking?limit=${limit}`);
    },

    /**
     * 获取个性化推荐
     */
    getPersonalizedRecommendations: function(limit = 6) {
        return this.call(`/api/personalized_recommendations?limit=${limit}`);
    },

    /**
     * 搜索学生
     */
    searchStudent: function(studentName) {
        return this.call(`/api/search_student?student_name=${encodeURIComponent(studentName)}`);
    }
};

// 图表管理器
const ChartManager = {
    charts: {},

    /**
     * 创建饼图
     */
    createPieChart: function(elementId, data, title = '') {
        const chart = echarts.init(document.getElementById(elementId));
        
        const option = {
            title: {
                text: title,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle'
            },
            series: [
                {
                    name: '出勤统计',
                    type: 'pie',
                    radius: '60%',
                    center: ['60%', '50%'],
                    data: data,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        formatter: '{b}: {c}\n({d}%)'
                    }
                }
            ]
        };

        chart.setOption(option);
        this.charts[elementId] = chart;
        
        // 响应式处理
        window.addEventListener('resize', () => {
            chart.resize();
        });

        return chart;
    },

    /**
     * 创建柱状图
     */
    createBarChart: function(elementId, data, title = '') {
        const chart = echarts.init(document.getElementById(elementId));
        
        const courseNames = data.map(item => item.course_name);
        const presentCounts = data.map(item => item.present_count);
        const absentCounts = data.map(item => item.absent_count);

        const option = {
            title: {
                text: title,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['出勤', '缺勤'],
                top: 30
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: courseNames,
                axisLabel: {
                    rotate: 45,
                    interval: 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '出勤',
                    type: 'bar',
                    data: presentCounts,
                    itemStyle: {
                        color: CONFIG.CHART_COLORS.success
                    }
                },
                {
                    name: '缺勤',
                    type: 'bar',
                    data: absentCounts,
                    itemStyle: {
                        color: CONFIG.CHART_COLORS.danger
                    }
                }
            ]
        };

        chart.setOption(option);
        this.charts[elementId] = chart;
        
        // 响应式处理
        window.addEventListener('resize', () => {
            chart.resize();
        });

        return chart;
    },

    /**
     * 销毁图表
     */
    destroyChart: function(elementId) {
        if (this.charts[elementId]) {
            this.charts[elementId].dispose();
            delete this.charts[elementId];
        }
    },

    /**
     * 调整所有图表大小
     */
    resizeAll: function() {
        Object.values(this.charts).forEach(chart => {
            chart.resize();
        });
    }
};

// 通知管理器
const NotificationManager = {
    /**
     * 显示成功通知
     */
    showSuccess: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },

    /**
     * 显示错误通知
     */
    showError: function(message, duration = 5000) {
        this.show(message, 'danger', duration);
    },

    /**
     * 显示警告通知
     */
    showWarning: function(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },

    /**
     * 显示信息通知
     */
    showInfo: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    },

    /**
     * 显示通知
     */
    show: function(message, type = 'info', duration = 3000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, duration);
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 添加页面加载动画
    document.body.classList.add('fade-in');

    // 响应式图表处理
    window.addEventListener('resize', Utils.debounce(() => {
        ChartManager.resizeAll();
    }, 250));
});

// 导出全局对象
window.AttendanceSystem = {
    Utils,
    API,
    ChartManager,
    NotificationManager,
    CONFIG
};
