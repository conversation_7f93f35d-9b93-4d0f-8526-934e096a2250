@charset "utf-8";

/********** Global **********/
/*
 *常用背景色： #0f1c30 #0b0f34 (6,64,102) (29,45,57) (7,33,58) (8,13,28) (15,43,36)
 */
html, body {
	width:100%;
	height:100%;
	min-height:635px;
	font-family:"microsoft yahei", arial, sans-serif;
	background-color:#152a59;
	background-repeat:no-repeat;
	background-position:center;
	background-size:100% 100%;
	overflow-x:hidden;
	overflow-y:auto;
}
body.bg06 {background-image:url("../img/bg06.png");}
.header {
	margin:0 auto;
	width:100%;
	height:55px;
	max-width:1920px;
	background:url("../img/header-left.png") left center no-repeat, url("../img/header-right.png") right center no-repeat;
	background-size:43% 100%, 43% 100%;
	overflow:hidden;
}
.header h3 {
	margin:0;
	padding:0;
	line-height:60px;
	text-align:center;
	font-size:24px;
	color:#5dc2fe;
}
@media (max-width: 1199px) {
	.header {
		background:url("../img/header-left.png") left bottom no-repeat, url("../img/header-right.png") right bottom no-repeat;
		background-size:100%, 100%;
	}
	.header h3 {line-height:48px;}
}
.wrapper {position:absolute;top:70px;bottom:0;left:0;right:0;min-height:555px;}
.container-fluid {height:100%;min-height:100%;}
.row {margin-left:-7px;margin-right:-8px;}
.row>div {padding-left:7px;padding-right:8px;}
.xpanel-wrapper {padding-bottom:15px;box-sizing:border-box;}
.xpanel-wrapper-1 {height:100%;}
.xpanel-wrapper-1-2 {height:50%;}
.xpanel-wrapper-1-3 {height:50%;}
.xpanel-wrapper-2-1 {height:90%;}
.xpanel-wrapper-2-2 {height:10%;}

.xpanel {
	padding:15px;
	height:100%;
	min-height:30px;
	background:url("../img/panel.png") center no-repeat;
	background-size:100% 100%;
	box-sizing:border-box;
}
.title{
    color:#00C6FB;
    font-weight:normal;
}
.list{
    font-size:10px;
    color:green;
}

/* tool */
.fill-h {height:100%;min-height:100%;}
.fill-h1 {height:90%;min-height:90%;}
.listbox {height:10%;min-height:5%;}
.no-margin {margin:0 !important;}
.no-padding {padding:0 !important;}
.no-bg {background:none !important;}
.no-border {border:0 !important;}

/* scrollbar */
::-webkit-scrollbar {width:0;height:0;}
::-webkit-scrollbar-track {background-color:transparent;}
::-webkit-scrollbar-thumb {border-radius:5px;background-color:rgba(0, 0, 0, 0.3);}