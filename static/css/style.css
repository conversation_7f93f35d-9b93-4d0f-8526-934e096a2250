/* 学生出勤管理系统自定义样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

/* 主容器样式 */
.container {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid #667eea;
    min-height: 70px;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: #2c3e50 !important;
    text-shadow: none;
}

.navbar-brand:hover {
    color: #1a252f !important;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.navbar-brand:focus {
    color: #2c3e50 !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    color: #2c3e50 !important;
    border-radius: 8px;
    margin: 0 5px;
    padding: 8px 15px !important;
}

.navbar-nav .nav-link:hover {
    color: #1a252f !important;
    background-color: rgba(44, 62, 80, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link:focus {
    color: #2c3e50 !important;
}

.navbar-nav .nav-link.active {
    background-color: rgba(44, 62, 80, 0.15);
    color: #1a252f !important;
}

/* 下拉菜单样式 */
.dropdown-menu {
    background-color: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.dropdown-item {
    color: #2c3e50;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateX(5px);
}

/* 导航栏切换按钮 */
.navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.95%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* 强制导航栏文字颜色 - 最高优先级 */
.navbar .navbar-brand,
.navbar .navbar-brand:hover,
.navbar .navbar-brand:focus,
.navbar .navbar-brand:visited {
    color: #2c3e50 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    font-size: 1.5rem !important;
    text-shadow: none !important;
}

.navbar .navbar-nav .nav-link,
.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link:focus,
.navbar .navbar-nav .nav-link:visited,
.navbar .navbar-nav .nav-link.active {
    color: #2c3e50 !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

.navbar .dropdown-toggle::after {
    border-top-color: #2c3e50 !important;
}

/* 覆盖Bootstrap默认样式 */
.navbar-dark .navbar-brand,
.navbar-dark .navbar-nav .nav-link {
    color: #2c3e50 !important;
}

.navbar-dark .navbar-brand:hover,
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
    color: #1a252f !important;
}

.navbar-dark .navbar-nav .show > .nav-link,
.navbar-dark .navbar-nav .nav-link.active {
    color: #1a252f !important;
}

/* 超级强制样式 - 确保导航栏文字可见 */
nav.navbar * {
    color: #2c3e50 !important;
}

nav.navbar a {
    color: #2c3e50 !important;
    text-decoration: none !important;
}

nav.navbar .fas,
nav.navbar .fa {
    color: #2c3e50 !important;
}

/* 主页Hero区域样式 - 专业前端优化 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    opacity: 0.6;
}

.hero-section > * {
    position: relative;
    z-index: 1;
}

.hero-section h1 {
    font-size: 3.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.hero-section p {
    font-size: 1.3rem;
    opacity: 0.95;
    font-weight: 300;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* 统计卡片优化 */
.stats-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stats-icon.text-success {
    color: #28a745 !important;
}

.stats-icon.text-danger {
    color: #dc3545 !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.25rem;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 统计卡片样式 */
.card.bg-success,
.card.bg-danger,
.card.bg-warning,
.card.bg-info {
    border: none;
    background: linear-gradient(135deg, var(--bs-success) 0%, #20c997 100%);
}

.card.bg-danger {
    background: linear-gradient(135deg, var(--bs-danger) 0%, #e74c3c 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #f39c12 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info) 0%, #3498db 100%);
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 徽章样式 */
.badge {
    font-size: 0.8em;
    padding: 0.5em 0.8em;
    border-radius: 20px;
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* 按钮样式 */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
    color: white;
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
}

/* 表单样式 */
.form-control {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background-color: white;
    transform: translateY(-1px);
}

.form-control-lg {
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    border-radius: 15px;
}

.form-select {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    background-color: rgba(255, 255, 255, 0.9);
}

.form-text {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* 输入组样式 */
.input-group {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.input-group .form-control {
    border-right: none;
    border-radius: 15px 0 0 15px;
    border: 2px solid #e9ecef;
    border-right: none;
}

.input-group .btn {
    border-left: none;
    border-radius: 0 15px 15px 0;
    border: 2px solid #667eea;
    border-left: none;
}

.input-group .form-control:focus {
    border-color: #667eea;
    border-right: none;
}

.input-group .form-control:focus + .btn {
    border-color: #667eea;
}

/* 列表组样式 */
.list-group-item {
    border: none;
    border-radius: 8px !important;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.list-group-item:hover {
    transform: translateX(5px);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.spinner-border.text-primary {
    color: #667eea !important;
}

/* 加载容器样式 */
#loading-indicator {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-top: none;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

footer p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-good {
    background-color: #28a745;
}

.status-warning {
    background-color: #ffc107;
}

.status-danger {
    background-color: #dc3545;
}

/* 页面标题样式 */
h1, h2, h3 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

h2 {
    font-size: 2rem;
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.text-muted {
    color: #6c757d !important;
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* 数据卡片特殊样式 */
.data-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.data-card .card-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.data-card h2 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
    color: white;
}

.data-card h2::after {
    display: none;
}
