# 学生考勤管理系统启动顺序说明

## 系统架构概述
本系统包含以下数据流：
- **A组（实时）**: Kafka生产者 → Kafka(A组)消费 → Spark流处理 → MySQL → Web可视化
- **B组（离线）**: Kaf<PERSON>生产者 → Kafka(B组)消费 → Flume → HDFS → Sqoop → Hive → Spark SQL → MySQL → Web可视化

## 启动顺序（必须按顺序执行）

### 第1步：初始化数据库
```bash
cd /root/job14
mysql -u root -p123456 < database_setup.sql
```

### 第2步：启动数据采集和处理服务

#### 2.1 启动Kafka生产者（数据源）
```bash
cd /root/job14
nohup python3 kafka_producer.py > logs/kafka_producer.log 2>&1 &
```

#### 2.2 启动Flume（B组数据流）
```bash
cd /export/server/flume
nohup bin/flume-ng agent --conf conf --conf-file /root/job14/flume_config.conf --name agent > /root/job14/logs/flume.log 2>&1 &

# 等待5秒
sleep 5
```

#### 2.3 启动Spark流处理（A组数据流）
```bash
cd /root/job14
nohup python3 spark_streaming_consumer.py > logs/spark_streaming.log 2>&1 &

# 等待5秒
sleep 5
```

#### 2.4 启动离线数据流处理器（B组数据流）
```bash
cd /root/job14
nohup python3 offline_data_flow.py > logs/offline_data_flow.log 2>&1 &

# 等待5秒
sleep 5
```

#### 2.5 启动协同过滤推荐服务（实时推荐）
```bash
cd /root/job14
nohup python3 collaborative_filtering.py > logs/collaborative_filtering.log 2>&1 &

# 等待5秒
sleep 5
```

### 第3步：启动Web应用
```bash
cd /root/job14
nohup python3 web_app.py > logs/web_app.log 2>&1 &

# 等待3秒
sleep 3
```

## 验证系统运行状态

### 检查进程状态
```bash
# 检查Python进程
ps aux | grep python3 | grep -v grep

# 检查Flume进程
ps aux | grep flume | grep -v grep
```

### 检查服务端口
```bash
# 检查Web应用端口
ss -tlnp | grep 5001
```

### 检查数据流状态
```bash
# 检查MySQL数据
mysql -u root -p123456 -e "USE attendance_db; SELECT COUNT(*) FROM raw_attendance_data;"

# 检查实时统计数据
mysql -u root -p123456 -e "USE attendance_db; SELECT * FROM attendance_summary ORDER BY update_time DESC LIMIT 1;"

# 检查推荐数据
mysql -u root -p123456 -e "USE attendance_db; SELECT COUNT(*) FROM user_recommendations;"

# 检查离线推荐数据
mysql -u root -p123456 -e "USE attendance_db; SELECT COUNT(*) FROM student_absence_ranking;"
```

## 访问系统

### Web界面
- 访问地址：http://localhost:5001
- 默认用户：admin / admin123

### 系统功能页面
1. **实时界面**: http://localhost:5001/realtime
2. **离线界面**: http://localhost:5001/offline  
3. **实时推荐**: http://localhost:5001/recommendations
4. **离线推荐**: http://localhost:5001/offline_recommendations
5. **学生搜索**: http://localhost:5001/search

## 停止系统

### 停止顺序（与启动相反）
```bash
# 1. 停止Web应用
pkill -f "web_app.py"

# 2. 停止数据处理服务
pkill -f "collaborative_filtering.py"
pkill -f "offline_data_flow.py"
pkill -f "spark_streaming_consumer.py"
pkill -f "flume"

# 3. 停止数据生产者
pkill -f "kafka_producer.py"
```

## 故障排查

### 常见问题
1. **数据库连接失败**: 检查MySQL服务状态和密码
2. **端口占用**: 使用`ss -tlnp | grep 端口号`检查
3. **Python依赖缺失**: 检查是否安装了所需的Python包

### 日志文件位置
- Kafka生产者: `/root/job14/logs/kafka_producer.log`
- Spark流处理: `/root/job14/logs/spark_streaming.log`
- Flume: `/root/job14/logs/flume.log`
- 离线数据流: `/root/job14/logs/offline_data_flow.log`
- 协同过滤: `/root/job14/logs/collaborative_filtering.log`
- Web应用: `/root/job14/logs/web_app.log`

### 检查日志命令
```bash
# 查看最新日志
tail -f logs/kafka_producer.log
tail -f logs/spark_streaming.log
tail -f logs/web_app.log

# 查看错误日志
grep -i error logs/*.log
```

## 注意事项
1. 必须严格按照启动顺序执行
2. 每个步骤之间需要等待指定时间
3. 确保MySQL服务正常运行
4. 系统启动完成后，数据会每5秒自动更新
5. 所有数据流都是真实实现，无模拟组件
6. 大数据组件（Kafka、HDFS、Hive等）假设已经启动并正常运行

## 快速启动脚本
如果需要一键启动所有服务，可以创建以下脚本：

```bash
#!/bin/bash
cd /root/job14

echo "初始化数据库..."
mysql -u root -p123456 < database_setup.sql

echo "启动Kafka生产者..."
nohup python3 kafka_producer.py > logs/kafka_producer.log 2>&1 &
sleep 2

echo "启动Flume..."
cd /export/server/flume
nohup bin/flume-ng agent --conf conf --conf-file /root/job14/flume_config.conf --name agent > /root/job14/logs/flume.log 2>&1 &
cd /root/job14
sleep 5

echo "启动Spark流处理..."
nohup python3 spark_streaming_consumer.py > logs/spark_streaming.log 2>&1 &
sleep 5

echo "启动离线数据流处理..."
nohup python3 offline_data_flow.py > logs/offline_data_flow.log 2>&1 &
sleep 5

echo "启动协同过滤推荐..."
nohup python3 collaborative_filtering.py > logs/collaborative_filtering.log 2>&1 &
sleep 5

echo "启动Web应用..."
nohup python3 web_app.py > logs/web_app.log 2>&1 &
sleep 3

echo "系统启动完成！"
echo "访问地址：http://localhost:5001"
```
