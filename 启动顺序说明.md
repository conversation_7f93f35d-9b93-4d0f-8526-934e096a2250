# 学生考勤管理系统启动顺序说明

## 系统架构概述
本系统包含以下数据流：
- **A组（实时）**: Kafka生产者 → Kafka(A组)消费 → Spark流处理 → MySQL → Web可视化
- **B组（离线）**: Kaf<PERSON>生产者 → Kafka(B组)消费 → Flume → HDFS → Sqoop → Hive → Spark SQL → MySQL → Web可视化

## 启动顺序（必须按顺序执行）

### 第1步：启动基础服务
```bash
# 1.1 启动Zookeeper
cd /export/server/kafka
nohup bin/zookeeper-server-start.sh config/zookeeper.properties > logs/zookeeper.log 2>&1 &

# 等待5秒
sleep 5

# 1.2 启动Kafka
nohup bin/kafka-server-start.sh config/server.properties > logs/kafka.log 2>&1 &

# 等待10秒
sleep 10

# 1.3 启动HDFS（如果未启动）
start-dfs.sh

# 1.4 启动Hive Metastore
cd /export/server/hive
nohup bin/hive --service metastore > logs/metastore.log 2>&1 &

# 等待5秒
sleep 5
```

### 第2步：初始化数据库
```bash
# 2.1 初始化MySQL数据库
cd /root/job14
mysql -u root -p123456 < database_setup.sql
```

### 第3步：启动数据采集和处理服务

#### 3.1 启动Kafka生产者（数据源）
```bash
cd /root/job14
nohup python3 kafka_producer.py > logs/kafka_producer.log 2>&1 &
```

#### 3.2 启动Flume（B组数据流）
```bash
cd /export/server/flume
nohup bin/flume-ng agent --conf conf --conf-file /root/job14/flume_config.conf --name agent > /root/job14/logs/flume.log 2>&1 &

# 等待5秒
sleep 5
```

#### 3.3 启动Spark流处理（A组数据流）
```bash
cd /root/job14
nohup python3 spark_streaming_consumer.py > logs/spark_streaming.log 2>&1 &

# 等待5秒
sleep 5
```

#### 3.4 启动离线数据流处理器（B组数据流）
```bash
cd /root/job14
nohup python3 offline_data_flow.py > logs/offline_data_flow.log 2>&1 &

# 等待5秒
sleep 5
```

#### 3.5 启动协同过滤推荐服务（实时推荐）
```bash
cd /root/job14
nohup python3 collaborative_filtering.py > logs/collaborative_filtering.log 2>&1 &

# 等待5秒
sleep 5
```

### 第4步：启动Web应用
```bash
cd /root/job14
nohup python3 web_app.py > logs/web_app.log 2>&1 &

# 等待3秒
sleep 3
```

## 验证系统运行状态

### 检查进程状态
```bash
# 检查Java进程
jps | grep -E "(Kafka|QuorumPeerMain|NameNode|DataNode)"

# 检查Python进程
ps aux | grep python3 | grep -v grep

# 检查Flume进程
ps aux | grep flume | grep -v grep
```

### 检查服务端口
```bash
# 检查Kafka端口
ss -tlnp | grep 9092

# 检查Web应用端口
ss -tlnp | grep 5001

# 检查Zookeeper端口
ss -tlnp | grep 2181
```

### 检查数据流状态
```bash
# 检查Kafka主题
/export/server/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092

# 检查Kafka消费者组
/export/server/kafka/bin/kafka-consumer-groups.sh --bootstrap-server localhost:9092 --list

# 检查HDFS数据
/export/server/hadoop/bin/hdfs dfs -ls /home/<USER>/attendance

# 检查MySQL数据
mysql -u root -p123456 -e "USE attendance_db; SELECT COUNT(*) FROM raw_attendance_data;"
```

## 访问系统

### Web界面
- 访问地址：http://localhost:5001
- 默认用户：admin / admin123

### 系统功能页面
1. **实时界面**: http://localhost:5001/realtime
2. **离线界面**: http://localhost:5001/offline  
3. **实时推荐**: http://localhost:5001/recommendations
4. **离线推荐**: http://localhost:5001/offline_recommendations
5. **学生搜索**: http://localhost:5001/search

## 停止系统

### 停止顺序（与启动相反）
```bash
# 1. 停止Web应用
pkill -f "web_app.py"

# 2. 停止数据处理服务
pkill -f "collaborative_filtering.py"
pkill -f "offline_data_flow.py" 
pkill -f "spark_streaming_consumer.py"
pkill -f "flume"

# 3. 停止数据生产者
pkill -f "kafka_producer.py"

# 4. 停止基础服务
pkill -f "kafka.Kafka"
pkill -f "QuorumPeerMain"
stop-dfs.sh
```

## 故障排查

### 常见问题
1. **Kafka连接失败**: 检查Zookeeper和Kafka是否正常启动
2. **数据库连接失败**: 检查MySQL服务状态和密码
3. **HDFS访问失败**: 检查Hadoop服务状态
4. **端口占用**: 使用`ss -tlnp | grep 端口号`检查

### 日志文件位置
- Kafka生产者: `/root/job14/logs/kafka_producer.log`
- Spark流处理: `/root/job14/logs/spark_streaming.log`
- Flume: `/root/job14/logs/flume.log`
- 离线数据流: `/root/job14/logs/offline_data_flow.log`
- 协同过滤: `/root/job14/logs/collaborative_filtering.log`
- Web应用: `/root/job14/logs/web_app.log`

## 注意事项
1. 必须严格按照启动顺序执行
2. 每个步骤之间需要等待指定时间
3. 确保所有依赖服务正常启动后再启动下一个服务
4. 系统启动完成后，数据会每5秒自动更新
5. 所有数据流都是真实实现，无模拟组件
