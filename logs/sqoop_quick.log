=== Sqoop数据抽取：HDFS → Hive ===
1. 创建Hive数据库...
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
Hive Session ID = fb3e9a37-47e3-46b5-986c-5749b7e8121d

Logging initialized using configuration in jar:file:/export/server/apache-hive-3.1.3-bin/lib/hive-common-3.1.3.jar!/hive-log4j2.properties Async: true
Loading class `com.mysql.jdbc.Driver'. This is deprecated. The new driver class is `com.mysql.cj.jdbc.Driver'. The driver is automatically registered via the SPI and manual loading of the driver class is generally unnecessary.
Hive Session ID = 17337c9f-c65d-42f2-b607-90c4a1cf463e
OK
Time taken: 1.627 seconds
WARN: The method class org.apache.commons.logging.impl.SLF4JLogFactory#release() was invoked.
WARN: Please see http://www.slf4j.org/codes.html#release for an explanation.
2. 检查HDFS数据文件...
Found 1 items
drwxr-xr-x   - root supergroup          0 2025-06-13 01:05 /home/<USER>/attendance/2025
3. 创建Hive外部表指向HDFS数据...
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
Hive Session ID = 0fe8b432-bd4b-4226-81ab-0d26ad8f69bd

Logging initialized using configuration in jar:file:/export/server/apache-hive-3.1.3-bin/lib/hive-common-3.1.3.jar!/hive-log4j2.properties Async: true
Loading class `com.mysql.jdbc.Driver'. This is deprecated. The new driver class is `com.mysql.cj.jdbc.Driver'. The driver is automatically registered via the SPI and manual loading of the driver class is generally unnecessary.
4. 验证Hive表数据...
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
Hive Session ID = fa401676-605d-4963-9d55-419f0bcedd05

Logging initialized using configuration in jar:file:/export/server/apache-hive-3.1.3-bin/lib/hive-common-3.1.3.jar!/hive-log4j2.properties Async: true
Loading class `com.mysql.jdbc.Driver'. This is deprecated. The new driver class is `com.mysql.cj.jdbc.Driver'. The driver is automatically registered via the SPI and manual loading of the driver class is generally unnecessary.
Hive Session ID = c33e4f37-d74d-4064-878b-0e16bce3d774
OK
Time taken: 1.647 seconds
Query ID = root_20250613110353_c82c0fb6-3be7-4eb1-b042-4f76859fe09b
Total jobs = 1
Launching Job 1 out of 1
Number of reduce tasks determined at compile time: 1
In order to change the average load for a reducer (in bytes):
  set hive.exec.reducers.bytes.per.reducer=<number>
In order to limit the maximum number of reducers:
  set hive.exec.reducers.max=<number>
In order to set a constant number of reducers:
  set mapreduce.job.reduces=<number>
Job running in-process (local Hadoop)
2025-06-13 11:04:04,586 Stage-1 map = 0%,  reduce = 0%
2025-06-13 11:04:05,616 Stage-1 map = 100%,  reduce = 100%
Ended Job = job_local1610076724_0001
MapReduce Jobs Launched: 
Stage-Stage-1:  HDFS Read: 71276 HDFS Write: 0 SUCCESS
Total MapReduce CPU Time Spent: 0 msec
OK
total_records
824
Time taken: 12.57 seconds, Fetched: 1 row(s)
Query ID = root_20250613110406_f3d53fda-57c7-4e55-8850-1ef50d8deb60
Total jobs = 1
Launching Job 1 out of 1
Number of reduce tasks not specified. Estimated from input data size: 1
In order to change the average load for a reducer (in bytes):
  set hive.exec.reducers.bytes.per.reducer=<number>
In order to limit the maximum number of reducers:
  set hive.exec.reducers.max=<number>
In order to set a constant number of reducers:
  set mapreduce.job.reduces=<number>
Job running in-process (local Hadoop)
2025-06-13 11:04:09,150 Stage-1 map = 0%,  reduce = 0%
2025-06-13 11:04:18,481 Stage-1 map = 100%,  reduce = 100%
Ended Job = job_local1487030646_0002
MapReduce Jobs Launched: 
Stage-Stage-1:  HDFS Read: 142552 HDFS Write: 0 SUCCESS
Total MapReduce CPU Time Spent: 0 msec
OK
class_number	count
BM2021	141
CS2021	117
CS2022	160
EE2021	147
EE2022	138
ME2021	121
Time taken: 12.812 seconds, Fetched: 6 row(s)
OK
attendance_data.class_number	attendance_data.student_name	attendance_data.course_name	attendance_data.student_id	attendance_data.score	attendance_data.attendance_status
EE2021	钱七	数据结构	2021EE002	100	A
EE2022	孙八	操作系统	2022CS002	100	A
EE2021	王十二	计算机网络	2021BM001	0	1
CS2022	李四	高等数学	2021CS002	100	A
CS2021	钱七	操作系统	2021EE002	0	1
Time taken: 0.492 seconds, Fetched: 5 row(s)
WARN: The method class org.apache.commons.logging.impl.SLF4JLogFactory#release() was invoked.
WARN: Please see http://www.slf4j.org/codes.html#release for an explanation.
=== Sqoop数据抽取完成 ===
数据流: Kafka(B组) → Flume → HDFS → Sqoop(外部表) → Hive
Hive表attendance_data现在指向HDFS中的实时数据
