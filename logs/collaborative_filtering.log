nohup: 忽略输入
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
=== 实时协同过滤推荐服务启动 ===
数据流: Kafka(A组)消费 -> Spark流处理 -> MySQL -> Web可视化
每5秒更新一次推荐...

[2025-06-13 16:00:20] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1248 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022EE001, 课程: 数据库原理, 分数: 0.4876
  2. 用户: 2022CS001, 课程: 信号处理, 分数: 0.4605
  3. 用户: 2021CS003, 课程: 电路分析, 分数: 0.4557
  4. 用户: 2021CS002, 课程: 算法设计, 分数: 0.4524
  5. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4505

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022EE001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS003, 推荐课程数: 1
  4. 学生ID: 2021CS002, 推荐课程数: 1
  5. 学生ID: 2021BM001, 推荐课程数: 1
  6. 学生ID: 2021CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:26] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1248 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 线性代数, 分数: 0.4624
  2. 用户: 2022CS001, 课程: 概率论, 分数: 0.4360
  3. 用户: 2021CS001, 课程: 算法设计, 分数: 0.4342
  4. 用户: 2021CS003, 课程: 数据库原理, 分数: 0.4328
  5. 用户: 2021BM001, 课程: 线性代数, 分数: 0.4201

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS001, 推荐课程数: 1
  4. 学生ID: 2021CS003, 推荐课程数: 1
  5. 学生ID: 2021BM001, 推荐课程数: 1
  6. 学生ID: 2021ME001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:31] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1248 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 高等数学, 分数: 0.4804
  2. 用户: 2022CS002, 课程: 电路分析, 分数: 0.4282
  3. 用户: 2022CS001, 课程: 电路分析, 分数: 0.4221
  4. 用户: 2021EE002, 课程: 线性代数, 分数: 0.4204
  5. 用户: 2021CS002, 课程: 电路分析, 分数: 0.4060

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2022CS002, 推荐课程数: 1
  3. 学生ID: 2022CS001, 推荐课程数: 1
  4. 学生ID: 2021EE002, 推荐课程数: 1
  5. 学生ID: 2021CS002, 推荐课程数: 1
  6. 学生ID: 2021CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:36] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1249 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS002, 课程: 概率论, 分数: 0.4754
  2. 用户: 2021EE002, 课程: 概率论, 分数: 0.4692
  3. 用户: 2022EE001, 课程: 数据库原理, 分数: 0.4565
  4. 用户: 2021CS002, 课程: 线性代数, 分数: 0.4114
  5. 用户: 2022CS001, 课程: 算法设计, 分数: 0.3902

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS002, 推荐课程数: 1
  2. 学生ID: 2021EE002, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2021CS002, 推荐课程数: 1
  5. 学生ID: 2022CS001, 推荐课程数: 1
  6. 学生ID: 2021CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:41] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1249 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 线性代数, 分数: 0.4917
  2. 用户: 2022CS002, 课程: 概率论, 分数: 0.4492
  3. 用户: 2021CS001, 课程: 算法设计, 分数: 0.4466
  4. 用户: 2021CS002, 课程: 数据库原理, 分数: 0.3814
  5. 用户: 2022EE001, 课程: 信号处理, 分数: 0.3666

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2022CS002, 推荐课程数: 1
  3. 学生ID: 2021CS001, 推荐课程数: 1
  4. 学生ID: 2021CS002, 推荐课程数: 1
  5. 学生ID: 2022EE001, 推荐课程数: 1
  6. 学生ID: 2021EE001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:46] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1250 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS001, 课程: 数据结构, 分数: 0.4788
  2. 用户: 2022CS002, 课程: 概率论, 分数: 0.4727
  3. 用户: 2021CS002, 课程: 计算机网络, 分数: 0.4690
  4. 用户: 2022EE001, 课程: 电路分析, 分数: 0.4681
  5. 用户: 2021BM001, 课程: 高等数学, 分数: 0.3697

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS001, 推荐课程数: 1
  2. 学生ID: 2022CS002, 推荐课程数: 1
  3. 学生ID: 2021CS002, 推荐课程数: 1
  4. 学生ID: 2022EE001, 推荐课程数: 1
  5. 学生ID: 2021BM001, 推荐课程数: 1
  6. 学生ID: 2022CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:52] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1250 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021BM001, 课程: 算法设计, 分数: 0.4955
  2. 用户: 2021CS001, 课程: 高等数学, 分数: 0.4892
  3. 用户: 2021CS003, 课程: 概率论, 分数: 0.4856
  4. 用户: 2021EE002, 课程: 概率论, 分数: 0.4535
  5. 用户: 2021CS002, 课程: 电路分析, 分数: 0.4179

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021BM001, 推荐课程数: 1
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
  2. 学生ID: 2021CS001, 推荐课程数: 1
  3. 学生ID: 2021CS003, 推荐课程数: 1
  4. 学生ID: 2021EE002, 推荐课程数: 1
  5. 学生ID: 2021CS002, 推荐课程数: 1
  6. 学生ID: 2022CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:00:57] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS003, 课程: 高等数学, 分数: 0.4712
  2. 用户: 2021CS002, 课程: 信号处理, 分数: 0.4393
  3. 用户: 2022CS001, 课程: 算法设计, 分数: 0.4262
  4. 用户: 2021EE002, 课程: 操作系统, 分数: 0.3803
  5. 用户: 2021ME001, 课程: 计算机网络, 分数: 0.3745

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS003, 推荐课程数: 1
  2. 学生ID: 2021CS002, 推荐课程数: 1
  3. 学生ID: 2022CS001, 推荐课程数: 1
  4. 学生ID: 2021EE002, 推荐课程数: 1
  5. 学生ID: 2021ME001, 推荐课程数: 1
  6. 学生ID: 2022CS002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:02] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS001, 课程: 概率论, 分数: 0.4908
  2. 用户: 2021ME001, 课程: 线性代数, 分数: 0.4737
  3. 用户: 2021CS001, 课程: 电路分析, 分数: 0.4302
  4. 用户: 2021BM001, 课程: 算法设计, 分数: 0.4288
  5. 用户: 2021CS003, 课程: 操作系统, 分数: 0.3804

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS001, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2021CS001, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2021CS003, 推荐课程数: 1
  6. 学生ID: 2021EE002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:07] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 数据结构, 分数: 0.4453
  2. 用户: 2021CS003, 课程: 数据库原理, 分数: 0.4424
  3. 用户: 2022EE001, 课程: 电路分析, 分数: 0.4288
  4. 用户: 2022CS002, 课程: 操作系统, 分数: 0.4170
  5. 用户: 2021CS001, 课程: 数据库原理, 分数: 0.4164

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2021CS003, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2022CS002, 推荐课程数: 1
  5. 学生ID: 2021CS001, 推荐课程数: 1
  6. 学生ID: 2021CS002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:12] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 电路分析, 分数: 0.4432
  2. 用户: 2022CS001, 课程: 计算机网络, 分数: 0.3668
  3. 用户: 2022EE001, 课程: 算法设计, 分数: 0.3588
  4. 用户: 2021CS003, 课程: 信号处理, 分数: 0.3520
  5. 用户: 2021CS002, 课程: 数据库原理, 分数: 0.3511

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2021CS003, 推荐课程数: 1
  5. 学生ID: 2021CS002, 推荐课程数: 1
  6. 学生ID: 2021BM001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:17] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS001, 课程: 数据结构, 分数: 0.4809
  2. 用户: 2021ME001, 课程: 数据结构, 分数: 0.4796
  3. 用户: 2021BM001, 课程: 概率论, 分数: 0.4666
  4. 用户: 2021CS001, 课程: 数据结构, 分数: 0.4582
  5. 用户: 2022EE001, 课程: 概率论, 分数: 0.4386

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS001, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2021BM001, 推荐课程数: 1
  4. 学生ID: 2021CS001, 推荐课程数: 1
  5. 学生ID: 2022EE001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:22] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 数据库原理, 分数: 0.4991
  2. 用户: 2021BM001, 课程: 高等数学, 分数: 0.4781
  3. 用户: 2021CS002, 课程: 算法设计, 分数: 0.4560
  4. 用户: 2022CS002, 课程: 电路分析, 分数: 0.4430
  5. 用户: 2022CS001, 课程: 线性代数, 分数: 0.4407

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2021BM001, 推荐课程数: 1
  3. 学生ID: 2021CS002, 推荐课程数: 1
  4. 学生ID: 2022CS002, 推荐课程数: 1
  5. 学生ID: 2022CS001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:28] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 线性代数, 分数: 0.4454
  2. 用户: 2021CS003, 课程: 电路分析, 分数: 0.4134
  3. 用户: 2021BM001, 课程: 操作系统, 分数: 0.4050
  4. 用户: 2022CS001, 课程: 操作系统, 分数: 0.4002
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
  5. 用户: 2022CS002, 课程: 信号处理, 分数: 0.3768

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2021CS003, 推荐课程数: 1
  3. 学生ID: 2021BM001, 推荐课程数: 1
  4. 学生ID: 2022CS001, 推荐课程数: 1
  5. 学生ID: 2022CS002, 推荐课程数: 1
  6. 学生ID: 2021EE001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:33] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1252 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022EE001, 课程: 数据库原理, 分数: 0.4915
  2. 用户: 2021CS003, 课程: 概率论, 分数: 0.4456
  3. 用户: 2021BM001, 课程: 操作系统, 分数: 0.4195
  4. 用户: 2022CS002, 课程: 数据库原理, 分数: 0.4048
  5. 用户: 2021EE002, 课程: 计算机网络, 分数: 0.3563

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022EE001, 推荐课程数: 1
  2. 学生ID: 2021CS003, 推荐课程数: 1
  3. 学生ID: 2021BM001, 推荐课程数: 1
  4. 学生ID: 2022CS002, 推荐课程数: 1
  5. 学生ID: 2021EE002, 推荐课程数: 1
  6. 学生ID: 2021CS002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:38] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1252 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS001, 课程: 高等数学, 分数: 0.4166
  2. 用户: 2021EE002, 课程: 数据结构, 分数: 0.4117
  3. 用户: 2021ME001, 课程: 数据库原理, 分数: 0.4099
  4. 用户: 2021CS003, 课程: 计算机网络, 分数: 0.4023
  5. 用户: 2021BM001, 课程: 算法设计, 分数: 0.3846

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS001, 推荐课程数: 1
  2. 学生ID: 2021EE002, 推荐课程数: 1
  3. 学生ID: 2021ME001, 推荐课程数: 1
  4. 学生ID: 2021CS003, 推荐课程数: 1
  5. 学生ID: 2021BM001, 推荐课程数: 1
  6. 学生ID: 2022CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:43] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1252 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS002, 课程: 算法设计, 分数: 0.4932
  2. 用户: 2021ME001, 课程: 线性代数, 分数: 0.4734
  3. 用户: 2022EE001, 课程: 算法设计, 分数: 0.4691
  4. 用户: 2021CS001, 课程: 算法设计, 分数: 0.4460
  5. 用户: 2021EE002, 课程: 电路分析, 分数: 0.4297

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS002, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2021CS001, 推荐课程数: 1
  5. 学生ID: 2021EE002, 推荐课程数: 1
  6. 学生ID: 2021BM001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:48] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1253 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 电路分析, 分数: 0.4606
  2. 用户: 2021CS002, 课程: 数据库原理, 分数: 0.4398
  3. 用户: 2021CS001, 课程: 算法设计, 分数: 0.4365
  4. 用户: 2021ME001, 课程: 计算机网络, 分数: 0.3730
  5. 用户: 2022CS001, 课程: 数据库原理, 分数: 0.3543

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2021CS002, 推荐课程数: 1
  3. 学生ID: 2021CS001, 推荐课程数: 1
  4. 学生ID: 2021ME001, 推荐课程数: 1
  5. 学生ID: 2022CS001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:53] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1253 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS002, 课程: 概率论, 分数: 0.4955
  2. 用户: 2021ME001, 课程: 信号处理, 分数: 0.4749
  3. 用户: 2022EE001, 课程: 概率论, 分数: 0.4479
  4. 用户: 2021BM001, 课程: 电路分析, 分数: 0.4226
  5. 用户: 2022CS001, 课程: 操作系统, 分数: 0.4133

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS002, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2022CS001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:01:59] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1253 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS001, 课程: 概率论, 分数: 0.4877
  2. 用户: 2021ME001, 课程: 计算机网络, 分数: 0.4872
  3. 用户: 2022CS002, 课程: 高等数学, 分数: 0.4639
  4. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4509
  5. 用户: 2021CS003, 课程: 操作系统, 分数: 0.4509

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS001, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2022CS002, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2021CS003, 推荐课程数: 1
  6. 学生ID: 2021EE002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:04] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1253 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS002, 课程: 线性代数, 分数: 0.4686
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
  2. 用户: 2021EE002, 课程: 数据结构, 分数: 0.4455
  3. 用户: 2021BM001, 课程: 线性代数, 分数: 0.4430
  4. 用户: 2022EE001, 课程: 操作系统, 分数: 0.4140
  5. 用户: 2022CS001, 课程: 操作系统, 分数: 0.4099

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS002, 推荐课程数: 1
  2. 学生ID: 2021EE002, 推荐课程数: 1
  3. 学生ID: 2021BM001, 推荐课程数: 1
  4. 学生ID: 2022EE001, 推荐课程数: 1
  5. 学生ID: 2022CS001, 推荐课程数: 1
  6. 学生ID: 2021ME001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:09] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1254 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 计算机网络, 分数: 0.4759
  2. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4684
  3. 用户: 2022CS001, 课程: 高等数学, 分数: 0.4382
  4. 用户: 2022EE001, 课程: 数据库原理, 分数: 0.3882
  5. 用户: 2021CS001, 课程: 电路分析, 分数: 0.3814

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2021BM001, 推荐课程数: 1
  3. 学生ID: 2022CS001, 推荐课程数: 1
  4. 学生ID: 2022EE001, 推荐课程数: 1
  5. 学生ID: 2021CS001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:14] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1255 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS002, 课程: 数据结构, 分数: 0.4876
  2. 用户: 2022EE001, 课程: 信号处理, 分数: 0.4730
  3. 用户: 2021ME001, 课程: 高等数学, 分数: 0.4714
  4. 用户: 2021CS001, 课程: 高等数学, 分数: 0.4186
  5. 用户: 2021CS003, 课程: 电路分析, 分数: 0.4065

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS002, 推荐课程数: 1
  2. 学生ID: 2022EE001, 推荐课程数: 1
  3. 学生ID: 2021ME001, 推荐课程数: 1
  4. 学生ID: 2021CS001, 推荐课程数: 1
  5. 学生ID: 2021CS003, 推荐课程数: 1
  6. 学生ID: 2022CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:19] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1255 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022CS001, 课程: 操作系统, 分数: 0.4554
  2. 用户: 2022CS002, 课程: 高等数学, 分数: 0.4515
  3. 用户: 2021CS002, 课程: 数据结构, 分数: 0.4395
  4. 用户: 2021ME001, 课程: 数据结构, 分数: 0.4316
  5. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4315

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022CS001, 推荐课程数: 1
  2. 学生ID: 2022CS002, 推荐课程数: 1
  3. 学生ID: 2021CS002, 推荐课程数: 1
  4. 学生ID: 2021ME001, 推荐课程数: 1
  5. 学生ID: 2021BM001, 推荐课程数: 1
  6. 学生ID: 2021CS003, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:24] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1255 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 线性代数, 分数: 0.4765
  2. 用户: 2021CS003, 课程: 数据结构, 分数: 0.4629
  3. 用户: 2021ME001, 课程: 操作系统, 分数: 0.4587
  4. 用户: 2021CS001, 课程: 高等数学, 分数: 0.4195
  5. 用户: 2021EE001, 课程: 概率论, 分数: 0.3988

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2021CS003, 推荐课程数: 1
  3. 学生ID: 2021ME001, 推荐课程数: 1
  4. 学生ID: 2021CS001, 推荐课程数: 1
  5. 学生ID: 2021EE001, 推荐课程数: 1
  6. 学生ID: 2022CS001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:30] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1256 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 线性代数, 分数: 0.4938
  2. 用户: 2022CS001, 课程: 计算机网络, 分数: 0.4749
  3. 用户: 2021CS002, 课程: 操作系统, 分数: 0.4453
  4. 用户: 2021BM001, 课程: 信号处理, 分数: 0.4354
  5. 用户: 2021EE002, 课程: 高等数学, 分数: 0.3689

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS002, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2021EE002, 推荐课程数: 1
  6. 学生ID: 2021EE001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:35] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1256 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4712
  2. 用户: 2022CS001, 课程: 数据结构, 分数: 0.4284
  3. 用户: 2021CS003, 课程: 线性代数, 分数: 0.4141
  4. 用户: 2021EE002, 课程: 算法设计, 分数: 0.4061
  5. 用户: 2022EE001, 课程: 算法设计, 分数: 0.3944

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021BM001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS003, 推荐课程数: 1
  4. 学生ID: 2021EE002, 推荐课程数: 1
  5. 学生ID: 2022EE001, 推荐课程数: 1
  6. 学生ID: 2021EE001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:02:40] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1256 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         /root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
1. 加载出勤数据...
   加载了 1235 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:53:58] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1237 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:54:28] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1239 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:54:58] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:55:29] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:55:59] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:56:29] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:56:59] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:57:30] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:58:00] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:58:30] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:59:00] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:59:30] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1240 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 16:00:00] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1248 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 16:00:31] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1248 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 16:01:01] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1251 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 16:01:31] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1252 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 16:02:01] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1253 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
