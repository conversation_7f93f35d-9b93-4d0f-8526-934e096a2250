/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
=== 实时协同过滤推荐服务启动 ===
数据流: Kafka(A组)消费 -> Spark流处理 -> MySQL -> Web可视化
每5秒更新一次推荐...

[2025-06-13 16:12:02] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1283 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021EE002, 课程: 信号处理, 分数: 0.4611
  2. 用户: 2021ME001, 课程: 高等数学, 分数: 0.4469
  3. 用户: 2022CS001, 课程: 线性代数, 分数: 0.3852
  4. 用户: 2021BM001, 课程: 数据结构, 分数: 0.3825
  5. 用户: 2021CS002, 课程: 操作系统, 分数: 0.3698

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021EE002, 推荐课程数: 1
  2. 学生ID: 2021ME001, 推荐课程数: 1
  3. 学生ID: 2022CS001, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2021CS002, 推荐课程数: 1
  6. 学生ID: 2022EE001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:08] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1283 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS001, 课程: 线性代数, 分数: 0.4997
  2. 用户: 2022CS001, 课程: 电路分析, 分数: 0.4959
  3. 用户: 2021CS003, 课程: 线性代数, 分数: 0.4867
  4. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4818
  5. 用户: 2021EE002, 课程: 线性代数, 分数: 0.4592

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS003, 推荐课程数: 1
  4. 学生ID: 2021BM001, 推荐课程数: 1
  5. 学生ID: 2021EE002, 推荐课程数: 1
  6. 学生ID: 2021CS002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:13] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1283 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2022EE001, 课程: 概率论, 分数: 0.4843
  2. 用户: 2022CS001, 课程: 数据库原理, 分数: 0.3983
  3. 用户: 2021ME001, 课程: 数据库原理, 分数: 0.3971
  4. 用户: 2021CS001, 课程: 计算机网络, 分数: 0.3919
  5. 用户: 2022CS002, 课程: 数据结构, 分数: 0.3900

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2022EE001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021ME001, 推荐课程数: 1
  4. 学生ID: 2021CS001, 推荐课程数: 1
  5. 学生ID: 2022CS002, 推荐课程数: 1
  6. 学生ID: 2021EE002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:18] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1283 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021ME001, 课程: 数据库原理, 分数: 0.4610
  2. 用户: 2022CS001, 课程: 数据结构, 分数: 0.4416
  3. 用户: 2021CS002, 课程: 信号处理, 分数: 0.4067
  4. 用户: 2022CS002, 课程: 算法设计, 分数: 0.3781
  5. 用户: 2021CS003, 课程: 高等数学, 分数: 0.3586

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021ME001, 推荐课程数: 1
  2. 学生ID: 2022CS001, 推荐课程数: 1
  3. 学生ID: 2021CS002, 推荐课程数: 1
  4. 学生ID: 2022CS002, 推荐课程数: 1
  5. 学生ID: 2021CS003, 推荐课程数: 1
  6. 学生ID: 2021EE002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:23] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1283 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS001, 课程: 电路分析, 分数: 0.4852
  2. 用户: 2021BM001, 课程: 电路分析, 分数: 0.4406
  3. 用户: 2022EE001, 课程: 概率论, 分数: 0.4354
  4. 用户: 2021ME001, 课程: 高等数学, 分数: 0.4345
  5. 用户: 2021CS003, 课程: 计算机网络, 分数: 0.4277

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021CS001, 推荐课程数: 1
  2. 学生ID: 2021BM001, 推荐课程数: 1
  3. 学生ID: 2022EE001, 推荐课程数: 1
  4. 学生ID: 2021ME001, 推荐课程数: 1
  5. 学生ID: 2021CS003, 推荐课程数: 1
  6. 学生ID: 2021EE002, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:29] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1284 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021BM001, 课程: 计算机网络, 分数: 0.4923
  2. 用户: 2022CS002, 课程: 数据库原理, 分数: 0.4497
  3. 用户: 2022CS001, 课程: 高等数学, 分数: 0.4435
  4. 用户: 2021CS003, 课程: 数据库原理, 分数: 0.3874
  5. 用户: 2021CS001, 课程: 高等数学, 分数: 0.3738

个性化推荐缺勤前6的学生名单:
  1. 学生ID: 2021BM001, 推荐课程数: 1
  2. 学生ID: 2022CS002, 推荐课程数: 1
  3. 学生ID: 2022CS001, 推荐课程数: 1
  4. 学生ID: 2021CS003, 推荐课程数: 1
  5. 学生ID: 2021CS001, 推荐课程数: 1
  6. 学生ID: 2021ME001, 推荐课程数: 1
等待5秒后进行下一次推荐...

[2025-06-13 16:12:34] 开始实时协同过滤推荐...
数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1284 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 6 条推荐记录
=== 协同过滤推荐完成 ===
✓ 生成了 6 条推荐
推荐结果示例:
  1. 用户: 2021CS002, 课程: 信号处理, 分数: 0.4766
  2. 用户: 2022CS001, 课程: 计算机网络, 分数: 0.4732
  3. 用户: 2021CS001, 课程: 线性代数, 分数: 0.4631
  4. 用户: 2021CS003, 课程: 概率论, 分数: 0.4343
  5. 用户: 2021ME001, 课程: 概率论, 分数: 0.4083

个性化推荐缺勤前6的学生名单:
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
