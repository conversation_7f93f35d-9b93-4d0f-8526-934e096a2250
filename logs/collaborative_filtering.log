/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
=== 协同过滤推荐服务启动 ===
每30秒更新一次推荐...

[2025-06-13 15:00:08] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:00:38] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:01:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:01:39] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:02:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:02:39] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:03:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:03:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:04:10] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:04:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1053 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:05:10] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1065 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:05:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1067 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:06:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1069 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:06:41] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1071 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:07:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1073 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:07:41] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:08:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:08:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:09:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:09:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:10:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:10:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:11:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:11:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:12:13] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:12:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:13:13] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1076 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:13:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1076 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:14:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1078 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:14:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1080 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:15:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1083 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:15:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1084 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:16:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1087 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:16:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1088 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:17:15] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1089 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:17:45] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1092 条记录
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
