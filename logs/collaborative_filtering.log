/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
=== 协同过滤推荐服务启动 ===
每30秒更新一次推荐...

[2025-06-13 15:00:08] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:00:38] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:01:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:01:39] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:02:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:02:39] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:03:09] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:03:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:04:10] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1050 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:04:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1053 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:05:10] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1065 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:05:40] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1067 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:06:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1069 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:06:41] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1071 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:07:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1073 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:07:41] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:08:11] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:08:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:09:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:09:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:10:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:10:42] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:11:12] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:11:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:12:13] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:12:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1075 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:13:13] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1076 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:13:43] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1076 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:14:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1078 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:14:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1080 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:15:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1083 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:15:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1084 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:16:14] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1087 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:16:44] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1088 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:17:15] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1089 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:17:45] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1092 条记录
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:18:15] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1093 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:18:45] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1096 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:19:15] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1097 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:19:46] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1098 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:20:16] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1100 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:20:46] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1101 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:21:16] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1103 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:21:46] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1106 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:22:16] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1106 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:22:47] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1108 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:23:17] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1110 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:23:47] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1112 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:24:17] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1114 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:24:47] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1118 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:25:17] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1118 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:25:48] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1119 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:26:18] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1122 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)

[2025-06-13 15:26:48] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1124 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:27:18] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1124 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:27:48] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1126 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:28:19] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1127 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:28:49] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1128 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:29:19] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1129 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:29:49] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1131 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:30:19] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1134 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:30:50] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1135 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:31:20] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1137 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:31:50] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1138 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:32:20] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1141 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:32:50] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1143 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:33:20] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1145 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:33:51] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1147 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:34:21] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1150 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:34:51] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1150 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:35:21] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1151 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:35:51] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1151 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:36:21] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1154 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:36:52] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1154 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:37:22] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1156 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:37:52] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1158 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:38:22] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1159 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:38:52] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1162 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:39:23] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1165 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:39:53] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1167 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:40:23] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1168 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:40:53] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1171 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:41:23] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1172 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:41:54] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1172 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:42:24] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1176 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:42:54] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1177 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:43:24] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1180 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:43:54] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1180 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:44:25] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1182 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:44:55] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1185 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:45:25] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1191 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:45:55] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1194 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:46:25] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1197 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:46:55] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1200 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:47:26] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1203 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:47:56] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1205 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:48:26] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1208 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:48:56] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1210 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:49:26] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1214 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:49:57] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1216 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:50:27] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1218 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:50:57] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1221 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:51:27] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1223 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:51:57] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1227 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:52:27] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1228 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:52:58] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
1. 加载出勤数据...
   加载了 1230 条记录
2. 建立用户-课程关系矩阵...
用户-课程矩阵: (10, 10)
3. 建立课程-课程关系矩阵...
课程-课程矩阵: (10, 10)
4. 基于User-Base CF生成推荐...
5. 保存推荐结果...
保存了 0 条推荐记录
=== 协同过滤推荐完成 ===
✗ 未生成推荐结果
等待30秒后进行下一次推荐...

[2025-06-13 15:53:28] 开始协同过滤推荐...
=== 协同过滤推荐系统 ===
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
/root/job14/collaborative_filtering.py:35: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, conn)
