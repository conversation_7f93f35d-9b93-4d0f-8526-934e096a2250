nohup: 忽略输入
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 01:02:30,428 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 01:02:37,572 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 01:02:42,999 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-ab382ad3-cb62-48aa-b69e-19f2aef60cc1. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 01:02:43,001 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 01:02:45,317 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 01:02:45,690 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-76810cc4-33ac-424f-8c9f-01394c55e72b. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 01:02:45,690 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 01:02:45,790 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 01:02:46,019 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-890f80e2-9c99-4b24-86b2-ef7d56730bca. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 01:02:46,020 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 01:02:46,109 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 01:02:46,355 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-49958326-1761-48af-beeb-6a7c7aa9f1bf. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 01:02:46,355 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 01:02:46,447 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 01:02:50,809 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 5419 milliseconds

[Stage 1:>                                                          (0 + 0) / 1]

[Stage 1:>                  (0 + 0) / 1][Stage 3:>                (0 + 0) / 200]

[Stage 1:>                  (0 + 1) / 1][Stage 3:>               (0 + 23) / 200]

[Stage 1:>    (0 + 1) / 1][Stage 3:> (0 + 23) / 200][Stage 5:>  (0 + 0) / 200]

[Stage 1:>    (0 + 1) / 1][Stage 3:> (1 + 28) / 200][Stage 5:>  (0 + 0) / 200]

                                                                                

[Stage 3:====>          (61 + 25) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:=====>         (74 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:======>        (87 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:=======>      (107 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:(129 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(144 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(156 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(168 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(178 + 22) / 200][Stage 5:>  (0 + 3) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:>(192 + 8) / 200][Stage 5:> (8 + 17) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:>(197 + 3) / 200][Stage 5:>(23 + 22) / 200][Stage 6:>    (0 + 0) / 1]

                                                                                
2025-06-13 01:02:57,570 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 11117 milliseconds

[Stage 5:=====>         (73 + 25) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:======>        (88 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:=======>      (106 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:========>     (127 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:==========>   (148 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:===========>  (164 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:==============>(194 + 6) / 200][Stage 6:>                  (0 + 1) / 1]

[Stage 5:==============>(196 + 4) / 200][Stage 6:>                  (0 + 1) / 1]

                                                                                
2025-06-13 01:02:59,909 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 13796 milliseconds

[Stage 6:>                                                          (0 + 1) / 1]

                                                                                
2025-06-13 01:03:00,187 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 5187 milliseconds

[Stage 22:>                                                         (0 + 1) / 1]

                                                                                

[Stage 23:>                                                         (0 + 1) / 1]

                                                                                

[Stage 30:>                                                         (0 + 1) / 1]

                                                                                

[Stage 32:>                                                         (0 + 1) / 1]

                                                                                

[Stage 41:>                                                         (0 + 1) / 1]

                                                                                

[Stage 42:>                                                         (0 + 1) / 1]

                                                                                

[Stage 43:>                                                         (0 + 1) / 1]

                                                                                

[Stage 44:>                                                         (0 + 1) / 1]

                                                                                

[Stage 51:>                                                         (0 + 1) / 1]

                                                                                

[Stage 52:>                                                         (0 + 1) / 1]

                                                                                

[Stage 53:>                                                         (0 + 1) / 1]

                                                                                

[Stage 56:==========================================================(1 + 0) / 1]

                                                                                

[Stage 63:>                                                         (0 + 1) / 1]

                                                                                

[Stage 65:==========================================================(1 + 0) / 1]

                                                                                

[Stage 67:>                                                         (0 + 1) / 1]

                                                                                

[Stage 72:>                                                         (0 + 1) / 1]

                                                                                

[Stage 82:>                                                         (0 + 1) / 1]

                                                                                

[Stage 83:>                                                         (0 + 1) / 1]

                                                                                

[Stage 92:>                                                         (0 + 1) / 1]

                                                                                

[Stage 93:>                                                         (0 + 1) / 1]

                                                                                

[Stage 94:>                                                         (0 + 1) / 1]

                                                                                

[Stage 102:>                                                        (0 + 1) / 1]

                                                                                

[Stage 103:>                                                        (0 + 1) / 1]

                                                                                

[Stage 106:>                                                        (0 + 1) / 1]

                                                                                

[Stage 113:>                                                        (0 + 1) / 1]

                                                                                

[Stage 125:>                                                        (0 + 1) / 1]

                                                                                

[Stage 126:>                                                        (0 + 1) / 1]

                                                                                

[Stage 135:>                                                        (0 + 1) / 1]

                                                                                

[Stage 136:>                                                        (0 + 1) / 1]

                                                                                

[Stage 147:>                                                        (0 + 1) / 1]

                                                                                

[Stage 156:>                                                        (0 + 1) / 1]

                                                                                

[Stage 159:>                                                        (0 + 1) / 1]

                                                                                

[Stage 160:>                                                        (0 + 1) / 1]

                                                                                
