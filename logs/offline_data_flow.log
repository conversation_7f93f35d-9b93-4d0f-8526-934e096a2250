nohup: 忽略输入
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 16:06:39,600 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 16:06:41,956 WARN util.Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
2025-06-13 16:06:51,952 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 16:06:51,953 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist
2025-06-13 16:06:55,921 WARN session.SessionState: METASTORE_FILTER_HOOK will be ignored, since hive.security.authorization.manager is set to instance of HiveAuthorizerFactory.
2025-06-13 16:06:56,969 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:06:59,871 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 16:06:59,872 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist

[Stage 0:>                                                        (0 + 23) / 23]

                                                                                

[Stage 2:>                                                        (0 + 23) / 23]

[Stage 2:====>                                                    (2 + 21) / 23]

                                                                                

[Stage 5:================================>                     (120 + 32) / 200]

[Stage 5:=================================================>    (185 + 15) / 200]

                                                                                

[Stage 16:====================>                                 (76 + 30) / 200]

[Stage 16:=========================================>           (155 + 26) / 200]

                                                                                

[Stage 19:===>                                                 (12 + 188) / 200]

                                                                                
2025-06-13 16:07:23,622 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:07:38,575 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:07:52,210 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:06,179 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:20,210 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:34,357 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:47,936 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:01,681 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:15,043 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:28,180 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:41,665 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
启动离线数据流监控...
每5秒检查一次数据更新

=== 离线数据流处理开始 [2025-06-13 16:06:45.047572] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:19.757416] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:34.815016] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:48.107057] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:02.006262] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:15.995369] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:29.819252] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:43.885843] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:57.830054] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:10.963105] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:24.239979] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:37.871745] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
2025-06-13 16:09:55,286 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:08,912 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:22,784 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:36,431 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:50,548 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:04,828 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:18,400 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:32,371 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:46,561 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:12:00,445 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:12:18,531 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:50.994996] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:04.689642] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:18.639005] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:32.134265] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:46.209689] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:00.084045] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:14.399976] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:28.153256] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:42.041787] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:56.133954] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:12:11.123118] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
2025-06-13 16:12:39,210 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:12:55,791 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
RN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:06:53,346 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:06:07.879328] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:06:21.114125] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:06:34.129988] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:06:49.663004] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✗ Spark SQL处理失败: An error occurred while calling o1158.count.
: org.apache.spark.sql.catalyst.errors.package$TreeNodeException: execute, tree:
Exchange SinglePartition, ENSURE_REQUIREMENTS, [id=#8110]
+- *(2) HashAggregate(keys=[], functions=[partial_count(1)], output=[count#5283L])
   +- *(2) HashAggregate(keys=[course_name#5242], functions=[], output=[])
      +- Exchange hashpartitioning(course_name#5242, 200), ENSURE_REQUIREMENTS, [id=#8105]
         +- *(1) HashAggregate(keys=[course_name#5242], functions=[], output=[course_name#5242])
            +- *(1) Filter isnotnull(course_name#5242)
               +- Scan hive attendance.attendance_raw [course_name#5242], HiveTableRelation [`attendance`.`attendance_raw`, org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe, Data Cols: [class_number#5240, student_name#5241, course_name#5242, student_id#5243, score#5244, attendance_..., Partition Cols: [year#5246, month#5247, day#5248]]

	at org.apache.spark.sql.catalyst.errors.package$.attachTree(package.scala:56)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.doExecute(ShuffleExchangeExec.scala:163)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.InputAdapter.inputRDD(WholeStageCodegenExec.scala:525)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs(WholeStageCodegenExec.scala:453)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs$(WholeStageCodegenExec.scala:452)
	at org.apache.spark.sql.execution.InputAdapter.inputRDDs(WholeStageCodegenExec.scala:496)
	at org.apache.spark.sql.execution.aggregate.HashAggregateExec.inputRDDs(HashAggregateExec.scala:141)
	at org.apache.spark.sql.execution.WholeStageCodegenExec.doExecute(WholeStageCodegenExec.scala:746)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.SparkPlan.getByteArrayRdd(SparkPlan.scala:321)
	at org.apache.spark.sql.execution.SparkPlan.executeCollect(SparkPlan.scala:387)
	at org.apache.spark.sql.Dataset.$anonfun$count$1(Dataset.scala:3019)
	at org.apache.spark.sql.Dataset.$anonfun$count$1$adapted(Dataset.scala:3018)
	at org.apache.spark.sql.Dataset.$anonfun$withAction$1(Dataset.scala:3700)
	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$5(SQLExecution.scala:103)
	at org.apache.spark.sql.execution.SQLExecution$.withSQLConfPropagated(SQLExecution.scala:163)
	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$1(SQLExecution.scala:90)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.SQLExecution$.withNewExecutionId(SQLExecution.scala:64)
	at org.apache.spark.sql.Dataset.withAction(Dataset.scala:3698)
	at org.apache.spark.sql.Dataset.count(Dataset.scala:3018)
	at sun.reflect.GeneratedMethodAccessor220.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)
	at py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)
	at py4j.Gateway.invoke(Gateway.java:282)
	at py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)
	at py4j.commands.CallCommand.execute(CallCommand.java:79)
	at py4j.GatewayConnection.run(GatewayConnection.java:238)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.spark.sql.catalyst.errors.package$TreeNodeException: execute, tree:
Exchange hashpartitioning(course_name#5242, 200), ENSURE_REQUIREMENTS, [id=#8105]
+- *(1) HashAggregate(keys=[course_name#5242], functions=[], output=[course_name#5242])
   +- *(1) Filter isnotnull(course_name#5242)
      +- Scan hive attendance.attendance_raw [course_name#5242], HiveTableRelation [`attendance`.`attendance_raw`, org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe, Data Cols: [class_number#5240, student_name#5241, course_name#5242, student_id#5243, score#5244, attendance_..., Partition Cols: [year#5246, month#5247, day#5248]]

	at org.apache.spark.sql.catalyst.errors.package$.attachTree(package.scala:56)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.doExecute(ShuffleExchangeExec.scala:163)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.InputAdapter.inputRDD(WholeStageCodegenExec.scala:525)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs(WholeStageCodegenExec.scala:453)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs$(WholeStageCodegenExec.scala:452)
	at org.apache.spark.sql.execution.InputAdapter.inputRDDs(WholeStageCodegenExec.scala:496)
	at org.apache.spark.sql.execution.aggregate.HashAggregateExec.inputRDDs(HashAggregateExec.scala:141)
	at org.apache.spark.sql.execution.aggregate.HashAggregateExec.inputRDDs(HashAggregateExec.scala:141)
	at org.apache.spark.sql.execution.WholeStageCodegenExec.doExecute(WholeStageCodegenExec.scala:746)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.inputRDD$lzycompute(ShuffleExchangeExec.scala:118)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.inputRDD(ShuffleExchangeExec.scala:118)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.shuffleDependency$lzycompute(ShuffleExchangeExec.scala:151)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.shuffleDependency(ShuffleExchangeExec.scala:149)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.$anonfun$doExecute$1(ShuffleExchangeExec.scala:166)
	at org.apache.spark.sql.catalyst.errors.package$.attachTree(package.scala:52)
	... 39 more
Caused by: org.apache.spark.sql.catalyst.analysis.NoSuchTableException: Table or view 'attendance_raw' not found in database 'attendance'
	at org.apache.spark.sql.hive.client.HiveClient.$anonfun$getTable$1(HiveClient.scala:90)
	at scala.Option.getOrElse(Option.scala:189)
	at org.apache.spark.sql.hive.client.HiveClient.getTable(HiveClient.scala:90)
	at org.apache.spark.sql.hive.client.HiveClient.getTable$(HiveClient.scala:89)
	at org.apache.spark.sql.hive.client.HiveClientImpl.getTable(HiveClientImpl.scala:91)
	at org.apache.spark.sql.hive.client.HiveClient.getPartitions(HiveClient.scala:222)
	at org.apache.spark.sql.hive.client.HiveClient.getPartitions$(HiveClient.scala:218)
	at org.apache.spark.sql.hive.client.HiveClientImpl.getPartitions(HiveClientImpl.scala:91)
	at org.apache.spark.sql.hive.HiveExternalCatalog.$anonfun$listPartitions$1(HiveExternalCatalog.scala:1256)
	at org.apache.spark.sql.hive.HiveExternalCatalog.withClient(HiveExternalCatalog.scala:102)
	at org.apache.spark.sql.hive.HiveExternalCatalog.listPartitions(HiveExternalCatalog.scala:1253)
	at org.apache.spark.sql.catalyst.catalog.ExternalCatalogWithListener.listPartitions(ExternalCatalogWithListener.scala:254)
	at org.apache.spark.sql.catalyst.catalog.SessionCatalog.listPartitions(SessionCatalog.scala:1173)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.rawPartitions$lzycompute(HiveTableScanExec.scala:197)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.rawPartitions(HiveTableScanExec.scala:186)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.prunedPartitions$lzycompute(HiveTableScanExec.scala:180)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.prunedPartitions(HiveTableScanExec.scala:166)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.$anonfun$doExecute$2(HiveTableScanExec.scala:211)
	at org.apache.spark.util.Utils$.withDummyCallSite(Utils.scala:2535)
	at org.apache.spark.sql.hive.execution.HiveTableScanExec.doExecute(HiveTableScanExec.scala:211)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.InputAdapter.inputRDD(WholeStageCodegenExec.scala:525)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs(WholeStageCodegenExec.scala:453)
	at org.apache.spark.sql.execution.InputRDDCodegen.inputRDDs$(WholeStageCodegenExec.scala:452)
	at org.apache.spark.sql.execution.InputAdapter.inputRDDs(WholeStageCodegenExec.scala:496)
	at org.apache.spark.sql.execution.FilterExec.inputRDDs(basicPhysicalOperators.scala:149)
	at org.apache.spark.sql.execution.aggregate.HashAggregateExec.inputRDDs(HashAggregateExec.scala:141)
	at org.apache.spark.sql.execution.WholeStageCodegenExec.doExecute(WholeStageCodegenExec.scala:746)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$execute$1(SparkPlan.scala:180)
	at org.apache.spark.sql.execution.SparkPlan.$anonfun$executeQuery$1(SparkPlan.scala:218)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.sql.execution.SparkPlan.executeQuery(SparkPlan.scala:215)
	at org.apache.spark.sql.execution.SparkPlan.execute(SparkPlan.scala:176)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.inputRDD$lzycompute(ShuffleExchangeExec.scala:118)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.inputRDD(ShuffleExchangeExec.scala:118)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.shuffleDependency$lzycompute(ShuffleExchangeExec.scala:151)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.shuffleDependency(ShuffleExchangeExec.scala:149)
	at org.apache.spark.sql.execution.exchange.ShuffleExchangeExec.$anonfun$doExecute$1(ShuffleExchangeExec.scala:166)
	at org.apache.spark.sql.catalyst.errors.package$.attachTree(package.scala:52)
	... 63 more
2025-06-13 16:07:05,003 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:07:21,585 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:07:34,128 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:07:46,863 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:00,061 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:13,764 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:26,955 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:40,616 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:08:54,614 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:07,749 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:21,169 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:09:34,581 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025


=== 离线数据流处理开始 [2025-06-13 16:07:00.951011] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:17.899002] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:30.930164] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:43.038448] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:07:55.990231] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:09.359920] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:22.986085] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:36.537097] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:08:49.894079] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:03.765112] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:17.292142] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:30.237132] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
2025-06-13 16:09:47,818 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:01,672 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:15,545 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:29,325 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:43,051 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:10:57,341 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:11,148 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:24,884 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:38,773 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:11:53,222 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:12:07,891 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025

[Stage 1126:============================>                      (110 + 25) / 200]

                                                                                
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:43.793000] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:09:57.426897] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:11.170236] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:25.081996] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:38.634107] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:10:52.756647] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:07.067004] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:20.491791] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:34.086079] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:11:48.451744] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:12:03.034000] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===

2025-06-13 16:12:30,050 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025

[Stage 1152:=========================================>         (161 + 25) / 200]

                                                                                
2025-06-13 16:12:46,975 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:13:01,914 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
