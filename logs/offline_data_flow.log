nohup: 忽略输入
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 16:00:19,595 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 16:00:21,329 WARN util.Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
2025-06-13 16:00:21,330 WARN util.Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.
2025-06-13 16:00:21,330 WARN util.Utils: Service 'SparkUI' could not bind on port 4042. Attempting port 4043.
2025-06-13 16:00:31,554 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 16:00:31,555 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist
2025-06-13 16:00:35,946 WARN session.SessionState: METASTORE_FILTER_HOOK will be ignored, since hive.security.authorization.manager is set to instance of HiveAuthorizerFactory.
2025-06-13 16:00:36,993 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:00:39,993 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 16:00:39,994 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist

[Stage 0:>                                                        (0 + 21) / 21]

[Stage 0:==>                                                      (1 + 20) / 21]

                                                                                

[Stage 2:>                                                        (0 + 21) / 21]

[Stage 2:===================>                                     (7 + 14) / 21]

                                                                                

[Stage 4:>                                                        (0 + 21) / 21]

                                                                                

[Stage 19:===============================>                     (119 + 81) / 200]

                                                                                
2025-06-13 16:01:03,178 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:01:18,416 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:01:33,481 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:01:48,053 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:02:01,606 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:02:15,793 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:02:29,604 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:02:43,350 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:02:56,914 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:03:11,132 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:03:25,513 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
启动离线数据流监控...
每5秒检查一次数据更新

=== 离线数据流处理开始 [2025-06-13 16:00:23.876470] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:00:59.079646] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:01:14.277193] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:01:29.123447] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:01:43.919127] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:01:57.908283] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:02:11.405130] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:02:25.586440] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:02:39.318436] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:02:52.625963] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:03:06.784357] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
执行Spark RDD处理 - 班级统计...
✓ RDD结果已保存到MySQL
✓ RDD处理完成，处理了6个班级
执行Spark SQL处理 - 课程统计...
✓ SQL结果已保存到MySQL
✓ Spark SQL处理完成，处理了8门课程
执行离线推荐处理 - 缺席最多的学生排名...
✓ 离线推荐结果已保存到MySQL
✓ 离线推荐处理完成，排名了20名学生
=== 离线数据流处理完成 ===


=== 离线数据流处理开始 [2025-06-13 16:03:21.041136] ===
数据流: Kafka(B组) -> Flume -> HDFS -> Sqoop -> Hive -> Spark SQL -> MySQL
✓ HDFS中发现Flume采集的数据
设置Hive数据库...
✓ Hive数据库和表设置完成
执行Sqoop数据导入...
✓ Sqoop导入完成
2025-06-13 16:03:39,714 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:03:53,939 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:04:08,199 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
2025-06-13 16:04:22,517 WARN command.AlterTableRecoverPartitionsCommand: ignore hdfs://node1:8020/home/<USER>/attendance/2025
