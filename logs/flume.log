nohup: 忽略输入
Info: Including Hadoop libraries found via (/export/server/hadoop/bin/hadoop) for HDFS access
Info: Including HBASE libraries found via (/export/server/hbase/bin/hbase) for HBASE access
Info: Including Hive libraries found via (/export/server/hive) for Hive access
+ exec /usr/lib/jvm/java-8-openjdk-amd64/bin/java -Xmx20m -cp '/export/server/flume/conf:/export/server/flume/lib/*:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hbase/conf:/usr/lib/jvm/java-8-openjdk-amd64/lib/tools.jar:/export/server/hbase:/export/server/hbase/lib/shaded-clients/hbase-shaded-client-byo-hadoop-2.5.7.jar:/export/server/hbase/lib/client-facing-thirdparty/audience-annotations-0.13.0.jar:/export/server/hbase/lib/client-facing-thirdparty/commons-logging-1.2.jar:/export/server/hbase/lib/client-facing-thirdparty/htrace-core4-4.1.0-incubating.jar:/export/server/hbase/lib/client-facing-thirdparty/jcl-over-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/jul-to-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-api-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-context-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-semconv-1.15.0-alpha.jar:/export/server/hbase/lib/client-facing-thirdparty/slf4j-api-1.7.33.jar:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-core-4.5.10.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-kms-2.11.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-ram-3.1.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-sdk-oss-3.13.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aws-java-sdk-bundle-1.12.367.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-data-lake-store-sdk-2.3.9.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-keyvault-core-1.0.0.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-storage-7.0.1.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aliyun-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archive-logs-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archives-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aws-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-datalake-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-client-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-datajoin-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-distcp-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-blockgen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-infra-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-workload-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-extras-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-fs2img-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-gridmix-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-kafka-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-minicluster-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-resourceestimator-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-rumen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-sls-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-streaming-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hamcrest-core-1.3.jar:/export/server/hadoop/share/hadoop/tools/lib/ini4j-0.5.4.jar:/export/server/hadoop/share/hadoop/tools/lib/jdk.tools-1.8.jar:/export/server/hadoop/share/hadoop/tools/lib/jdom2-2.0.6.jar:/export/server/hadoop/share/hadoop/tools/lib/junit-4.13.2.jar:/export/server/hadoop/share/hadoop/tools/lib/kafka-clients-2.8.2.jar:/export/server/hadoop/share/hadoop/tools/lib/lz4-java-1.7.1.jar:/export/server/hadoop/share/hadoop/tools/lib/ojalgo-43.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-api-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-noop-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-util-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/org.jacoco.agent-0.8.5-runtime.jar:/export/server/hadoop/share/hadoop/tools/lib/wildfly-openssl-1.1.3.Final.jar:/export/server/hadoop/share/hadoop/tools/lib/zstd-jni-1.4.9-1.jar:/export/server/hbase/conf:/export/server/hive/lib/*' -Djava.library.path=:/export/server/hadoop/lib/native:/export/server/hadoop/lib/native org.apache.flume.node.Application --conf-file /root/job14/flume_config.conf --name agent
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-flume-1.11.0-bin/lib/log4j-slf4j-impl-2.18.0.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
2025-06-13T16:05:42,163 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,172 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,172 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,173 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T16:05:42,174 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T16:05:42,175 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,175 INFO  [main] conf.FlumeConfiguration: Added sinks: hdfs-sink Agent: agent
2025-06-13T16:05:42,175 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T16:05:42,176 WARN  [main] conf.FlumeConfiguration: Agent configuration for 'agent' has no configfilters.
2025-06-13T16:05:42,219 INFO  [main] conf.FlumeConfiguration: Post-validation flume configuration contains configuration for agents: [agent]
2025-06-13T16:05:42,221 INFO  [main] node.AbstractConfigurationProvider: Creating channels
2025-06-13T16:05:42,229 INFO  [main] channel.DefaultChannelFactory: Creating instance of channel memory-channel type memory
2025-06-13T16:05:42,237 INFO  [main] node.AbstractConfigurationProvider: Created channel memory-channel
2025-06-13T16:05:42,239 INFO  [main] source.DefaultSourceFactory: Creating instance of source kafka-source, type org.apache.flume.source.kafka.KafkaSource
2025-06-13T16:05:42,289 INFO  [main] sink.DefaultSinkFactory: Creating instance of sink: hdfs-sink, type: hdfs
2025-06-13T16:05:42,301 INFO  [main] node.AbstractConfigurationProvider: Channel memory-channel connected to [kafka-source, hdfs-sink]
2025-06-13T16:05:42,301 INFO  [main] node.Application: Initializing components
2025-06-13T16:05:42,301 INFO  [main] node.Application: Starting new configuration:{ sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE} counterGroup:{ name:null counters:{} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T16:05:42,302 INFO  [main] node.Application: Starting Channel memory-channel
2025-06-13T16:05:42,307 INFO  [main] node.Application: Waiting for channel: memory-channel to start. Sleeping for 500 ms
2025-06-13T16:05:42,308 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: CHANNEL, name: memory-channel: Successfully registered new MBean.
2025-06-13T16:05:42,308 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: CHANNEL, name: memory-channel started
2025-06-13T16:05:42,807 INFO  [main] node.Application: Starting Sink hdfs-sink
2025-06-13T16:05:42,808 INFO  [main] node.Application: Starting Source kafka-source
2025-06-13T16:05:42,809 INFO  [lifecycleSupervisor-1-2] kafka.KafkaSource: Starting org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE}...
2025-06-13T16:05:42,810 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SINK, name: hdfs-sink: Successfully registered new MBean.
2025-06-13T16:05:42,810 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: SINK, name: hdfs-sink started
2025-06-13T16:05:42,856 INFO  [lifecycleSupervisor-1-2] consumer.ConsumerConfig: 	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	socket.connection.setup.timeout.max.ms = 127000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.ByteArrayDeserializer

2025-06-13T16:05:42,988 INFO  [lifecycleSupervisor-1-2] utils.AppInfoParser: Kafka version: 2.7.2
2025-06-13T16:05:42,988 INFO  [lifecycleSupervisor-1-2] utils.AppInfoParser: Kafka commitId: 37a1cc36bf4d76f3
2025-06-13T16:05:42,988 INFO  [lifecycleSupervisor-1-2] utils.AppInfoParser: Kafka startTimeMs: 1749801942986
2025-06-13T16:05:42,991 INFO  [lifecycleSupervisor-1-2] consumer.KafkaConsumer: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Subscribed to topic(s): attendance
2025-06-13T16:05:42,992 INFO  [lifecycleSupervisor-1-2] kafka.KafkaSource: Kafka source kafka-source started.
2025-06-13T16:05:42,992 INFO  [lifecycleSupervisor-1-2] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SOURCE, name: kafka-source: Successfully registered new MBean.
2025-06-13T16:05:42,993 INFO  [lifecycleSupervisor-1-2] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source started
2025-06-13T16:05:43,563 INFO  [PollableSourceRunner-KafkaSource-kafka-source] clients.Metadata: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Cluster ID: iiC77VKpQGi25v7rIpF1nw
2025-06-13T16:05:43,566 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Discovered group coordinator node1:9092 (id: 2147483647 rack: null)
2025-06-13T16:05:43,569 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T16:05:43,588 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T16:05:45,008 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully joined group with generation Generation{generationId=4, memberId='consumer-offline_group_b-1-39b3fbdc-8cd3-4a45-a5f5-3e4ca9b191f7', protocol='range'}
2025-06-13T16:05:45,013 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully synced group in generation Generation{generationId=4, memberId='consumer-offline_group_b-1-39b3fbdc-8cd3-4a45-a5f5-3e4ca9b191f7', protocol='range'}
2025-06-13T16:05:45,015 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Notifying assignor about the new Assignment(partitions=[attendance-0, attendance-1])
2025-06-13T16:05:45,032 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Adding newly assigned partitions: attendance-0, attendance-1
2025-06-13T16:05:45,033 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 0 assigned.
2025-06-13T16:05:45,033 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 1 assigned.
2025-06-13T16:05:45,048 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Setting offset for partition attendance-0 to the committed offset FetchPosition{offset=198, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T16:05:45,048 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Setting offset for partition attendance-1 to the committed offset FetchPosition{offset=192, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T16:05:46,834 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T16:05:47,090 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749801946835.txt.tmp
2025-06-13T16:10:49,611 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T16:10:49,614 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749801946835.txt.tmp
2025-06-13T16:10:49,669 INFO  [hdfs-hdfs-sink-call-runner-6] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749801946835.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749801946835.txt
2025-06-13T16:10:59,397 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T16:10:59,421 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749802259398.txt.tmp
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              2025-06-13T16:05:44,414 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Attempt to heartbeat failed since group is rebalancing
2025-06-13T16:05:44,415 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Revoke previously assigned partitions attendance-0, attendance-1, attendance-2
2025-06-13T16:05:44,416 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 0 revoked.
2025-06-13T16:05:44,416 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 1 revoked.
2025-06-13T16:05:44,416 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 2 revoked.
2025-06-13T16:05:44,416 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] (Re-)joining group
2025-06-13T16:05:44,420 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Successfully joined group with generation Generation{generationId=4, memberId='consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302', protocol='range'}
2025-06-13T16:05:44,421 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Finished assignment for group at generation 4: {consumer-offline_group_b-1-39b3fbdc-8cd3-4a45-a5f5-3e4ca9b191f7=Assignment(partitions=[attendance-0, attendance-1]), consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302=Assignment(partitions=[attendance-2])}
2025-06-13T16:05:44,425 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Successfully synced group in generation Generation{generationId=4, memberId='consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302', protocol='range'}
2025-06-13T16:05:44,426 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Notifying assignor about the new Assignment(partitions=[attendance-2])
2025-06-13T16:05:44,426 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Adding newly assigned partitions: attendance-2
2025-06-13T16:05:44,426 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 2 assigned.
2025-06-13T16:05:44,428 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Setting offset for partition attendance-2 to the committed offset FetchPosition{offset=187, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T16:10:30,455 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T16:10:30,455 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749801930395.txt.tmp
2025-06-13T16:10:30,472 INFO  [hdfs-hdfs-sink-call-runner-7] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749801930395.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749801930395.txt
2025-06-13T16:10:35,572 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T16:10:35,588 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749802235573.txt.tmp
