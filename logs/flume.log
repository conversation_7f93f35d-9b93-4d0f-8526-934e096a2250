nohup: 忽略输入
Info: Including Hadoop libraries found via (/export/server/hadoop/bin/hadoop) for HDFS access
Info: Including HBASE libraries found via (/export/server/hbase/bin/hbase) for HBASE access
Info: Including Hive libraries found via (/export/server/hive) for Hive access
+ exec /usr/lib/jvm/java-8-openjdk-amd64/bin/java -Xmx20m -cp '/export/server/flume/conf:/export/server/flume/lib/*:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hbase/conf:/usr/lib/jvm/java-8-openjdk-amd64/lib/tools.jar:/export/server/hbase:/export/server/hbase/lib/shaded-clients/hbase-shaded-client-byo-hadoop-2.5.7.jar:/export/server/hbase/lib/client-facing-thirdparty/audience-annotations-0.13.0.jar:/export/server/hbase/lib/client-facing-thirdparty/commons-logging-1.2.jar:/export/server/hbase/lib/client-facing-thirdparty/htrace-core4-4.1.0-incubating.jar:/export/server/hbase/lib/client-facing-thirdparty/jcl-over-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/jul-to-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-api-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-context-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-semconv-1.15.0-alpha.jar:/export/server/hbase/lib/client-facing-thirdparty/slf4j-api-1.7.33.jar:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-core-4.5.10.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-kms-2.11.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-ram-3.1.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-sdk-oss-3.13.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aws-java-sdk-bundle-1.12.367.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-data-lake-store-sdk-2.3.9.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-keyvault-core-1.0.0.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-storage-7.0.1.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aliyun-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archive-logs-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archives-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aws-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-datalake-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-client-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-datajoin-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-distcp-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-blockgen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-infra-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-workload-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-extras-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-fs2img-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-gridmix-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-kafka-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-minicluster-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-resourceestimator-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-rumen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-sls-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-streaming-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hamcrest-core-1.3.jar:/export/server/hadoop/share/hadoop/tools/lib/ini4j-0.5.4.jar:/export/server/hadoop/share/hadoop/tools/lib/jdk.tools-1.8.jar:/export/server/hadoop/share/hadoop/tools/lib/jdom2-2.0.6.jar:/export/server/hadoop/share/hadoop/tools/lib/junit-4.13.2.jar:/export/server/hadoop/share/hadoop/tools/lib/kafka-clients-2.8.2.jar:/export/server/hadoop/share/hadoop/tools/lib/lz4-java-1.7.1.jar:/export/server/hadoop/share/hadoop/tools/lib/ojalgo-43.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-api-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-noop-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-util-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/org.jacoco.agent-0.8.5-runtime.jar:/export/server/hadoop/share/hadoop/tools/lib/wildfly-openssl-1.1.3.Final.jar:/export/server/hadoop/share/hadoop/tools/lib/zstd-jni-1.4.9-1.jar:/export/server/hbase/conf:/export/server/hive/lib/*' -Djava.library.path=:/export/server/hadoop/lib/native:/export/server/hadoop/lib/native org.apache.flume.node.Application --conf-file /root/job14/flume_config.conf --name agent
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-flume-1.11.0-bin/lib/log4j-slf4j-impl-2.18.0.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
2025-06-13T15:21:12,543 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,549 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,549 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:12,549 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,550 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:12,551 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,551 INFO  [main] conf.FlumeConfiguration: Added sinks: hdfs-sink Agent: agent
2025-06-13T15:21:12,551 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:12,551 WARN  [main] conf.FlumeConfiguration: Agent configuration for 'agent' has no configfilters.
2025-06-13T15:21:12,590 INFO  [main] conf.FlumeConfiguration: Post-validation flume configuration contains configuration for agents: [agent]
2025-06-13T15:21:12,592 INFO  [main] node.AbstractConfigurationProvider: Creating channels
2025-06-13T15:21:12,603 INFO  [main] channel.DefaultChannelFactory: Creating instance of channel memory-channel type memory
2025-06-13T15:21:12,610 INFO  [main] node.AbstractConfigurationProvider: Created channel memory-channel
2025-06-13T15:21:12,611 INFO  [main] source.DefaultSourceFactory: Creating instance of source kafka-source, type org.apache.flume.source.kafka.KafkaSource
2025-06-13T15:21:12,666 INFO  [main] sink.DefaultSinkFactory: Creating instance of sink: hdfs-sink, type: hdfs
2025-06-13T15:21:12,682 INFO  [main] node.AbstractConfigurationProvider: Channel memory-channel connected to [kafka-source, hdfs-sink]
2025-06-13T15:21:12,682 INFO  [main] node.Application: Initializing components
2025-06-13T15:21:12,682 INFO  [main] node.Application: Starting new configuration:{ sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE} counterGroup:{ name:null counters:{} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T15:21:12,683 INFO  [main] node.Application: Starting Channel memory-channel
2025-06-13T15:21:12,685 INFO  [main] node.Application: Waiting for channel: memory-channel to start. Sleeping for 500 ms
2025-06-13T15:21:12,686 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: CHANNEL, name: memory-channel: Successfully registered new MBean.
2025-06-13T15:21:12,686 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: CHANNEL, name: memory-channel started
2025-06-13T15:21:13,186 INFO  [main] node.Application: Starting Sink hdfs-sink
2025-06-13T15:21:13,188 INFO  [main] node.Application: Starting Source kafka-source
2025-06-13T15:21:13,189 INFO  [lifecycleSupervisor-1-1] kafka.KafkaSource: Starting org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE}...
2025-06-13T15:21:13,190 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SINK, name: hdfs-sink: Successfully registered new MBean.
2025-06-13T15:21:13,190 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: SINK, name: hdfs-sink started
2025-06-13T15:21:13,292 INFO  [lifecycleSupervisor-1-1] consumer.ConsumerConfig: 	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	socket.connection.setup.timeout.max.ms = 127000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.ByteArrayDeserializer

2025-06-13T15:21:13,516 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka version: 2.7.2
2025-06-13T15:21:13,516 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka commitId: 37a1cc36bf4d76f3
2025-06-13T15:21:13,516 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka startTimeMs: 1749799273513
2025-06-13T15:21:13,519 INFO  [lifecycleSupervisor-1-1] consumer.KafkaConsumer: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Subscribed to topic(s): attendance
2025-06-13T15:21:13,519 INFO  [lifecycleSupervisor-1-1] kafka.KafkaSource: Kafka source kafka-source started.
2025-06-13T15:21:13,519 INFO  [lifecycleSupervisor-1-1] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SOURCE, name: kafka-source: Successfully registered new MBean.
2025-06-13T15:21:13,520 INFO  [lifecycleSupervisor-1-1] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source started
2025-06-13T15:21:14,266 INFO  [PollableSourceRunner-KafkaSource-kafka-source] clients.Metadata: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Cluster ID: iiC77VKpQGi25v7rIpF1nw
2025-06-13T15:21:14,269 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Discovered group coordinator node1:9092 (id: 2147483647 rack: null)
2025-06-13T15:21:14,273 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T15:21:14,303 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T15:21:14,311 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully joined group with generation Generation{generationId=1, memberId='consumer-offline_group_b-1-1fa7801d-6dcd-4273-a20f-4b273f253f30', protocol='range'}
2025-06-13T15:21:14,315 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Finished assignment for group at generation 1: {consumer-offline_group_b-1-1fa7801d-6dcd-4273-a20f-4b273f253f30=Assignment(partitions=[attendance-0, attendance-1, attendance-2])}
2025-06-13T15:21:14,327 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully synced group in generation Generation{generationId=1, memberId='consumer-offline_group_b-1-1fa7801d-6dcd-4273-a20f-4b273f253f30', protocol='range'}
2025-06-13T15:21:14,327 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Notifying assignor about the new Assignment(partitions=[attendance-0, attendance-1, attendance-2])
2025-06-13T15:21:14,330 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Adding newly assigned partitions: attendance-0, attendance-1, attendance-2
2025-06-13T15:21:14,330 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 0 assigned.
2025-06-13T15:21:14,330 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 1 assigned.
2025-06-13T15:21:14,330 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 2 assigned.
2025-06-13T15:21:14,350 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Found no committed offset for partition attendance-0
2025-06-13T15:21:14,350 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Found no committed offset for partition attendance-1
2025-06-13T15:21:14,350 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Found no committed offset for partition attendance-2
2025-06-13T15:21:14,362 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.SubscriptionState: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Resetting offset for partition attendance-0 to position FetchPosition{offset=48, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}.
2025-06-13T15:21:14,364 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.SubscriptionState: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Resetting offset for partition attendance-1 to position FetchPosition{offset=43, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}.
2025-06-13T15:21:14,364 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.SubscriptionState: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Resetting offset for partition attendance-2 to position FetchPosition{offset=47, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}.
2025-06-13T15:21:19,561 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:21:19,764 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749799279562.txt.tmp
2025-06-13T15:21:43,196 INFO  [conf-file-poller-0] node.FileConfigurationSource: Reloading configuration file:/root/job14/flume_config.conf
2025-06-13T15:21:43,199 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,199 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,199 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:43,199 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,200 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Added sinks: hdfs-sink Agent: agent
2025-06-13T15:21:43,201 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T15:21:43,201 WARN  [conf-file-poller-0] conf.FlumeConfiguration: Agent configuration for 'agent' has no configfilters.
2025-06-13T15:21:43,205 INFO  [conf-file-poller-0] conf.FlumeConfiguration: Post-validation flume configuration contains configuration for agents: [agent]
2025-06-13T15:21:43,205 INFO  [conf-file-poller-0] node.AbstractConfigurationProvider: Creating channels
2025-06-13T15:21:43,205 INFO  [conf-file-poller-0] node.AbstractConfigurationProvider: Created channel memory-channel
2025-06-13T15:21:43,206 INFO  [conf-file-poller-0] source.DefaultSourceFactory: Creating instance of source kafka-source, type org.apache.flume.source.kafka.KafkaSource
2025-06-13T15:21:43,206 INFO  [conf-file-poller-0] sink.DefaultSinkFactory: Creating instance of sink: hdfs-sink, type: hdfs
2025-06-13T15:21:43,206 INFO  [conf-file-poller-0] node.AbstractConfigurationProvider: Channel memory-channel connected to [kafka-source, hdfs-sink]
2025-06-13T15:21:43,221 INFO  [conf-file-poller-0] node.Application: Shutting down configuration: { sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:START} counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.polls=16, runner.backoffs=10} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.backoffs=3} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T15:21:43,221 INFO  [conf-file-poller-0] node.Application: Stopping Source kafka-source
2025-06-13T15:21:43,222 INFO  [conf-file-poller-0] lifecycle.LifecycleSupervisor: Stopping component: PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:START} counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.polls=16, runner.backoffs=10} } }
2025-06-13T15:21:43,222 ERROR [PollableSourceRunner-KafkaSource-kafka-source] kafka.KafkaSource: KafkaSource EXCEPTION, {}
org.apache.kafka.common.errors.InterruptException: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1292) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1233) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1206) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.flume.source.kafka.KafkaSource.doProcess(KafkaSource.java:247) ~[flume-kafka-source-1.11.0.jar:1.11.0]
	at org.apache.flume.source.AbstractPollableSource.process(AbstractPollableSource.java:60) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.source.PollableSourceRunner$PollingRunner.run(PollableSourceRunner.java:133) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.InterruptedException
	... 10 more
2025-06-13T15:21:43,232 INFO  [PollableSourceRunner-KafkaSource-kafka-source] source.PollableSourceRunner: Source runner interrupted. Exiting
2025-06-13T15:21:43,234 INFO  [conf-file-poller-0] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Revoke previously assigned partitions attendance-0, attendance-1, attendance-2
2025-06-13T15:21:43,235 INFO  [conf-file-poller-0] kafka.SourceRebalanceListener: topic attendance - partition 0 revoked.
2025-06-13T15:21:43,235 INFO  [conf-file-poller-0] kafka.SourceRebalanceListener: topic attendance - partition 1 revoked.
2025-06-13T15:21:43,235 INFO  [conf-file-poller-0] kafka.SourceRebalanceListener: topic attendance - partition 2 revoked.
2025-06-13T15:21:43,235 INFO  [conf-file-poller-0] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Member consumer-offline_group_b-1-1fa7801d-6dcd-4273-a20f-4b273f253f30 sending LeaveGroup request to coordinator node1:9092 (id: 2147483647 rack: null) due to the consumer is being closed
2025-06-13T15:21:43,243 INFO  [conf-file-poller-0] metrics.Metrics: Metrics scheduler closed
2025-06-13T15:21:43,244 INFO  [conf-file-poller-0] metrics.Metrics: Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-13T15:21:43,244 INFO  [conf-file-poller-0] metrics.Metrics: Metrics reporters closed
2025-06-13T15:21:43,256 INFO  [conf-file-poller-0] utils.AppInfoParser: App info kafka.consumer for consumer-offline_group_b-1 unregistered
2025-06-13T15:21:43,256 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source stopped
2025-06-13T15:21:43,256 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.start.time == 1749799273520
2025-06-13T15:21:43,257 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.stop.time == 1749799303256
2025-06-13T15:21:43,257 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.commit.time == 66
2025-06-13T15:21:43,257 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.empty.count == 15
2025-06-13T15:21:43,258 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.event.get.time == 5005
2025-06-13T15:21:43,258 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append-batch.accepted == 0
2025-06-13T15:21:43,258 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append-batch.received == 0
2025-06-13T15:21:43,260 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append.accepted == 0
2025-06-13T15:21:43,260 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append.received == 0
2025-06-13T15:21:43,260 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.channel.write.fail == 0
2025-06-13T15:21:43,260 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.event.read.fail == 1
2025-06-13T15:21:43,261 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.events.accepted == 5
2025-06-13T15:21:43,261 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.events.received == 5
2025-06-13T15:21:43,261 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.generic.processing.fail == 0
2025-06-13T15:21:43,261 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.open-connection.count == 0
2025-06-13T15:21:43,261 INFO  [conf-file-poller-0] kafka.KafkaSource: Kafka Source kafka-source stopped. Metrics: SOURCE:kafka-source{src.events.accepted=5, src.open-connection.count=0, src.append.received=0, src.channel.write.fail=0, source.kafka.event.get.time=5005, source.kafka.empty.count=15, src.append-batch.accepted=0, src.event.read.fail=1, src.append-batch.received=0, src.generic.processing.fail=0, src.append.accepted=0, src.events.received=5, source.kafka.commit.time=66}
2025-06-13T15:21:43,262 INFO  [conf-file-poller-0] node.Application: Stopping Sink hdfs-sink
2025-06-13T15:21:43,262 INFO  [conf-file-poller-0] lifecycle.LifecycleSupervisor: Stopping component: SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.backoffs=3} } }
2025-06-13T15:21:43,263 ERROR [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSEventSink: process failed
java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.BucketWriter.checkAndThrowInterruptedException(BucketWriter.java:708) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.flush(BucketWriter.java:477) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:439) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.DefaultSinkProcessor.process(DefaultSinkProcessor.java:39) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.SinkRunner$PollingRunner.run(SinkRunner.java:145) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
2025-06-13T15:21:43,264 ERROR [SinkRunner-PollingRunner-DefaultSinkProcessor] flume.SinkRunner: Unable to deliver event. Exception follows.
org.apache.flume.EventDeliveryException: java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:462) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.DefaultSinkProcessor.process(DefaultSinkProcessor.java:39) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.SinkRunner$PollingRunner.run(SinkRunner.java:145) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.BucketWriter.checkAndThrowInterruptedException(BucketWriter.java:708) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.flush(BucketWriter.java:477) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:439) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	... 3 more
2025-06-13T15:21:48,267 INFO  [conf-file-poller-0] hdfs.HDFSEventSink: Closing /home/<USER>/attendance/2025/06/13/attendance-data
2025-06-13T15:21:48,272 INFO  [conf-file-poller-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749799279562.txt.tmp
2025-06-13T15:21:48,323 INFO  [hdfs-hdfs-sink-call-runner-1] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749799279562.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749799279562.txt
2025-06-13T15:21:48,335 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Component type: SINK, name: hdfs-sink stopped
2025-06-13T15:21:48,336 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.start.time == 1749799273190
2025-06-13T15:21:48,336 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.stop.time == 1749799308335
2025-06-13T15:21:48,336 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.batch.complete == 0
2025-06-13T15:21:48,336 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.batch.empty == 3
2025-06-13T15:21:48,337 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.batch.underflow == 4
2025-06-13T15:21:48,337 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.channel.read.fail == 0
2025-06-13T15:21:48,337 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.connection.closed.count == 1
2025-06-13T15:21:48,337 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.connection.creation.count == 1
2025-06-13T15:21:48,337 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.connection.failed.count == 0
2025-06-13T15:21:48,338 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.event.drain.attempt == 5
2025-06-13T15:21:48,338 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.event.drain.sucess == 4
2025-06-13T15:21:48,338 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SINK, name: hdfs-sink. sink.event.write.fail == 1
2025-06-13T15:21:48,338 INFO  [lifecycleSupervisor-1-6] lifecycle.LifecycleSupervisor: Component has already been stopped SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{runner.deliveryErrors=1, runner.backoffs.consecutive=1, runner.backoffs=3} } }
2025-06-13T15:21:48,338 INFO  [conf-file-poller-0] node.Application: Stopping Channel memory-channel
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] lifecycle.LifecycleSupervisor: Stopping component: org.apache.flume.channel.MemoryChannel{name: memory-channel}
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Component type: CHANNEL, name: memory-channel stopped
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.start.time == 1749799272686
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.stop.time == 1749799308339
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.capacity == 10000
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.current.size == 1
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.event.put.attempt == 5
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.event.put.success == 5
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.event.take.attempt == 12
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: CHANNEL, name: memory-channel. channel.event.take.success == 4
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] node.Application: Initializing components
2025-06-13T15:21:48,339 INFO  [conf-file-poller-0] node.Application: Starting new configuration:{ sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE} counterGroup:{ name:null counters:{} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@4fe00e5b counterGroup:{ name:null counters:{} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T15:21:48,340 INFO  [conf-file-poller-0] node.Application: Starting Channel memory-channel
2025-06-13T15:21:48,340 INFO  [conf-file-poller-0] node.Application: Waiting for channel: memory-channel to start. Sleeping for 500 ms
2025-06-13T15:21:48,340 INFO  [lifecycleSupervisor-1-3] instrumentation.MonitoredCounterGroup: Component type: CHANNEL, name: memory-channel started
2025-06-13T15:21:48,842 INFO  [conf-file-poller-0] node.Application: Starting Sink hdfs-sink
2025-06-13T15:21:48,842 INFO  [conf-file-poller-0] node.Application: Starting Source kafka-source
2025-06-13T15:21:48,843 INFO  [lifecycleSupervisor-1-9] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SINK, name: hdfs-sink: Successfully registered new MBean.
2025-06-13T15:21:48,844 INFO  [lifecycleSupervisor-1-9] instrumentation.MonitoredCounterGroup: Component type: SINK, name: hdfs-sink started
2025-06-13T15:21:48,847 INFO  [lifecycleSupervisor-1-5] kafka.KafkaSource: Starting org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE}...
2025-06-13T15:21:48,852 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:21:48,852 INFO  [lifecycleSupervisor-1-5] consumer.ConsumerConfig: 	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	socket.connection.setup.timeout.max.ms = 127000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.ByteArrayDeserializer

2025-06-13T15:21:48,889 INFO  [lifecycleSupervisor-1-5] utils.AppInfoParser: Kafka version: 2.7.2
2025-06-13T15:21:48,892 INFO  [lifecycleSupervisor-1-5] utils.AppInfoParser: Kafka commitId: 37a1cc36bf4d76f3
2025-06-13T15:21:48,892 INFO  [lifecycleSupervisor-1-5] utils.AppInfoParser: Kafka startTimeMs: 1749799308889
2025-06-13T15:21:48,892 INFO  [lifecycleSupervisor-1-5] consumer.KafkaConsumer: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Subscribed to topic(s): attendance
2025-06-13T15:21:48,893 INFO  [lifecycleSupervisor-1-5] kafka.KafkaSource: Kafka source kafka-source started.
2025-06-13T15:21:48,893 INFO  [lifecycleSupervisor-1-5] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SOURCE, name: kafka-source: Successfully registered new MBean.
2025-06-13T15:21:48,893 INFO  [lifecycleSupervisor-1-5] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source started
2025-06-13T15:21:48,905 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749799308852.txt.tmp
2025-06-13T15:21:48,908 INFO  [PollableSourceRunner-KafkaSource-kafka-source] clients.Metadata: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Cluster ID: iiC77VKpQGi25v7rIpF1nw
2025-06-13T15:21:48,911 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Discovered group coordinator node1:9092 (id: 2147483647 rack: null)
2025-06-13T15:21:48,913 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] (Re-)joining group
2025-06-13T15:21:48,922 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] (Re-)joining group
2025-06-13T15:21:48,941 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Successfully joined group with generation Generation{generationId=3, memberId='consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302', protocol='range'}
2025-06-13T15:21:48,944 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Finished assignment for group at generation 3: {consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302=Assignment(partitions=[attendance-0, attendance-1, attendance-2])}
2025-06-13T15:21:48,953 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Successfully synced group in generation Generation{generationId=3, memberId='consumer-offline_group_b-2-a8af95ae-77d5-4e7a-8a82-f9b30b87e302', protocol='range'}
2025-06-13T15:21:48,954 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Notifying assignor about the new Assignment(partitions=[attendance-0, attendance-1, attendance-2])
2025-06-13T15:21:48,954 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Adding newly assigned partitions: attendance-0, attendance-1, attendance-2
2025-06-13T15:21:48,954 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 0 assigned.
2025-06-13T15:21:48,954 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 1 assigned.
2025-06-13T15:21:48,955 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 2 assigned.
2025-06-13T15:21:48,958 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Setting offset for partition attendance-0 to the committed offset FetchPosition{offset=51, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T15:21:48,958 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Setting offset for partition attendance-1 to the committed offset FetchPosition{offset=44, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T15:21:48,958 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-2, groupId=offline_group_b] Setting offset for partition attendance-2 to the committed offset FetchPosition{offset=48, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}
2025-06-13T15:26:48,964 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T15:26:48,970 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749799308852.txt.tmp
2025-06-13T15:26:49,000 INFO  [hdfs-hdfs-sink-call-runner-2] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749799308852.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749799308852.txt
2025-06-13T15:26:50,428 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:26:50,466 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749799610429.txt.tmp
2025-06-13T15:31:50,502 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T15:31:50,504 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749799610429.txt.tmp
2025-06-13T15:31:50,520 INFO  [hdfs-hdfs-sink-call-runner-1] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749799610429.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749799610429.txt
2025-06-13T15:31:50,940 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:31:50,980 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749799910941.txt.tmp
2025-06-13T15:36:51,004 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T15:36:51,008 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749799910941.txt.tmp
2025-06-13T15:36:51,038 INFO  [hdfs-hdfs-sink-call-runner-8] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749799910941.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749799910941.txt
2025-06-13T15:36:51,417 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:36:51,455 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749800211418.txt.tmp
2025-06-13T15:41:51,503 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T15:41:51,506 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749800211418.txt.tmp
2025-06-13T15:41:51,526 INFO  [hdfs-hdfs-sink-call-runner-8] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749800211418.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749800211418.txt
2025-06-13T15:41:51,881 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:41:51,911 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749800511882.txt.tmp
2025-06-13T15:46:51,938 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T15:46:51,940 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749800511882.txt.tmp
2025-06-13T15:46:51,957 INFO  [hdfs-hdfs-sink-call-runner-4] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749800511882.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749800511882.txt
2025-06-13T15:46:52,320 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T15:46:52,344 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749800812321.txt.tmp
