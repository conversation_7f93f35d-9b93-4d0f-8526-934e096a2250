SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 16:12:01,998 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 16:12:04,461 WARN util.Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
2025-06-13 16:12:04,462 WARN util.Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.
2025-06-13 16:12:13,194 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 16:12:13,195 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist

[Stage 0:>                                                          (0 + 0) / 1]

[Stage 0:>                                                          (0 + 1) / 1]

                                                                                

[Stage 2:>                                                        (0 + 24) / 25]

[Stage 2:>                                                        (0 + 25) / 25]

[Stage 2:==========================>                             (12 + 13) / 25]

                                                                                

[Stage 4:>                                                        (0 + 24) / 25]

[Stage 4:====>                                                    (2 + 23) / 25]

                                                                                
