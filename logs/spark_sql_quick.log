SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 11:02:35,363 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 11:02:37,057 WARN util.Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
2025-06-13 11:02:37,058 WARN util.Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.
2025-06-13 11:02:43,156 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 11:02:43,156 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist

[Stage 0:>                                                          (0 + 0) / 1]

[Stage 0:>                                                          (0 + 1) / 1]

                                                                                

[Stage 3:>                                                        (0 + 12) / 12]

                                                                                

[Stage 9:>                                                        (0 + 12) / 12]

                                                                                
ERROR:root:Exception while sending command.
Traceback (most recent call last):
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/java_gateway.py", line 1200, in send_command
    answer = smart_decode(self.stream.readline()[:-1])
  File "/root/anaconda3/envs/spark_env/lib/python3.9/socket.py", line 716, in readinto
    return self._sock.recv_into(b)
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/context.py", line 284, in signal_handler
    self.cancelAllJobs()
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/context.py", line 1201, in cancelAllJobs
    self._jsc.sc().cancelAllJobs()
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/java_gateway.py", line 1304, in __call__
    return_value = get_return_value(
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/sql/utils.py", line 111, in deco
    return f(*a, **kw)
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/protocol.py", line 326, in get_return_value
    raise Py4JJavaError(
py4j.protocol.Py4JJavaError: An error occurred while calling o94.cancelAllJobs.
: java.lang.IllegalStateException: Cannot call methods on a stopped SparkContext.
This stopped SparkContext was created at:

org.apache.spark.api.java.JavaSparkContext.<init>(JavaSparkContext.scala:58)
sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
java.lang.reflect.Constructor.newInstance(Constructor.java:423)
py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:247)
py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)
py4j.Gateway.invoke(Gateway.java:238)
py4j.commands.ConstructorCommand.invokeConstructor(ConstructorCommand.java:80)
py4j.commands.ConstructorCommand.execute(ConstructorCommand.java:69)
py4j.GatewayConnection.run(GatewayConnection.java:238)
java.lang.Thread.run(Thread.java:750)

The currently active SparkContext was created at:

(No active SparkContext.)
         
	at org.apache.spark.SparkContext.assertNotStopped(SparkContext.scala:118)
	at org.apache.spark.SparkContext.cancelAllJobs(SparkContext.scala:2381)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)
	at py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)
	at py4j.Gateway.invoke(Gateway.java:282)
	at py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)
	at py4j.commands.CallCommand.execute(CallCommand.java:79)
	at py4j.GatewayConnection.run(GatewayConnection.java:238)
	at java.lang.Thread.run(Thread.java:750)


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/java_gateway.py", line 1033, in send_command
    response = connection.send_command(command)
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/java_gateway.py", line 1211, in send_command
    raise Py4JNetworkError(
py4j.protocol.Py4JNetworkError: Error while receiving
/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/context.py:460: RuntimeWarning: Unable to cleanly shutdown Spark JVM process. It is possible that the process has crashed, been killed or may also be in a zombie state.
  warnings.warn(
=== 启动离线SQL处理 ===
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL → MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
SQL处理已启动，正在监控Hive数据变化...
数据流严格按照: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL

离线SQL处理正在运行...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL → MySQL
按 Ctrl+C 停止
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检测到新数据: 824 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录

停止SQL处理...
