SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 15:00:09,686 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 15:00:11,536 WARN util.Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.
2025-06-13 15:00:11,537 WARN util.Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.
2025-06-13 15:00:18,023 WARN conf.HiveConf: HiveConf of name hive.metastore.event.db.notification.api.auth does not exist
2025-06-13 15:00:18,024 WARN conf.HiveConf: HiveConf of name hive.log.dir does not exist

[Stage 0:>                                                          (0 + 0) / 1]

[Stage 0:>                                                          (0 + 1) / 1]

                                                                                
=== 启动离线SQL处理 ===
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL → MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
SQL处理已启动，正在监控Hive数据变化...
数据流严格按照: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL

离线SQL处理正在运行...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → SQL → MySQL
按 Ctrl+C 停止
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检测到新数据: 824 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 824 条记录
SQL处理运行中... (监控Hive数据变化)
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 826 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 826 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 826 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 826 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 832 条记录
检测到新数据: 6 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 833 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 891 条记录
检测到新数据: 58 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 892 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 893 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 951 条记录
检测到新数据: 58 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 952 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
SQL处理运行中... (监控Hive数据变化)
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 953 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1011 条记录
检测到新数据: 58 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1012 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1013 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1071 条记录
检测到新数据: 58 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1072 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1073 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1131 条记录
检测到新数据: 58 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1132 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
SQL处理运行中... (监控Hive数据变化)
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1133 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 1192 条记录
检测到新数据: 59 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
SQL处理运行中... (监控Hive数据变化)
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1193 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检测到新数据: 31 条
SQL处理运行中... (监控Hive数据变化)
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
SQL处理运行中... (监控Hive数据变化)
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1224 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1226 条记录
检测到新数据: 2 条
成功保存 8 条Spark SQL结果到MySQL
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检测到新数据: 1 条
成功保存 8 条Spark SQL结果到MySQL
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
SQL处理运行中... (监控Hive数据变化)
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
正在从Hive数据仓库读取数据 (严格按照数据流)...
数据流: Kafka(B组) → Flume → HDFS → Sqoop → Hive → Spark SQL
从Hive读取到 1227 条记录
检查数据流前置步骤...
1. 检查Hive数据库...
   ✓ Hive数据库attendance已存在
2. 检查Hive表...
   ✓ Hive表attendance_data已存在
