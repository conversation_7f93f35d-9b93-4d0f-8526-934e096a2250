nohup: 忽略输入
 * Serving Flask app 'web_app'
 * Debug mode: off
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************00:5001
[33mPress CTRL+C to quit[0m
************ - - [13/Jun/2025 16:00:37] "GET / HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/js/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/js/echarts.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/css/fontawesome.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:38] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:38] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:38] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:38] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:40] "GET /realtime HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/css/fontawesome.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/js/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/js/echarts.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:40] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:40] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:40] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:45] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:45] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:45] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:50] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:50] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:50] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:55] "GET /api/attendance_summary HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:55] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:55] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:59] "GET /offline HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/css/fontawesome.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/js/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/js/echarts.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:00:59] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:00:59] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:04] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:04] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:09] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:09] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:14] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:14] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:18] "GET /recommendations HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/css/fontawesome.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/js/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/js/echarts.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:01:18] "GET /api/personalized_recommendations?limit=6 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:02] "GET /offline_recommendations HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/css/fontawesome.min.css HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/js/bootstrap.bundle.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/js/echarts.min.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "[36mGET /static/js/main.js HTTP/1.1[0m" 304 -
************ - - [13/Jun/2025 16:02:02] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:07] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:12] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:17] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:22] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:27] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:32] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:37] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:42] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:47] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:52] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:02:57] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
