nohup: 忽略输入
 * Serving Flask app 'web_app'
 * Debug mode: off
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************00:5001
[33mPress CTRL+C to quit[0m
************ - - [13/Jun/2025 16:08:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:08:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:08:06] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
127.0.0.1 - - [13/Jun/2025 16:09:01] "GET /api/offline_recommendations HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:09:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:09:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:09:06] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:10:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:10:06] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:10:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:11:06] "GET /api/class_attendance HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:11:06] "GET /api/course_top5 HTTP/1.1" 200 -
************ - - [13/Jun/2025 16:11:06] "GET /api/offline_recommendations?limit=10 HTTP/1.1" 200 -
