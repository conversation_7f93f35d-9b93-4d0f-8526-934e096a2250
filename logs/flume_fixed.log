nohup: 忽略输入
Info: Including Hadoop libraries found via (/export/server/hadoop/bin/hadoop) for HDFS access
Info: Including HBASE libraries found via (/export/server/hbase/bin/hbase) for HBASE access
Info: Including Hive libraries found via (/export/server/hive) for Hive access
+ exec /usr/lib/jvm/java-8-openjdk-amd64/bin/java -Xmx20m -Dflume.root.logger=INFO,console -cp 'conf:/export/server/flume/lib/*:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hbase/conf:/usr/lib/jvm/java-8-openjdk-amd64/lib/tools.jar:/export/server/hbase:/export/server/hbase/lib/shaded-clients/hbase-shaded-client-byo-hadoop-2.5.7.jar:/export/server/hbase/lib/client-facing-thirdparty/audience-annotations-0.13.0.jar:/export/server/hbase/lib/client-facing-thirdparty/commons-logging-1.2.jar:/export/server/hbase/lib/client-facing-thirdparty/htrace-core4-4.1.0-incubating.jar:/export/server/hbase/lib/client-facing-thirdparty/jcl-over-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/jul-to-slf4j-1.7.33.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-api-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-context-1.15.0.jar:/export/server/hbase/lib/client-facing-thirdparty/opentelemetry-semconv-1.15.0-alpha.jar:/export/server/hbase/lib/client-facing-thirdparty/slf4j-api-1.7.33.jar:/export/server/hadoop/etc/hadoop:/export/server/hadoop/share/hadoop/common/lib/*:/export/server/hadoop/share/hadoop/common/*:/export/server/hadoop/share/hadoop/hdfs:/export/server/hadoop/share/hadoop/hdfs/lib/*:/export/server/hadoop/share/hadoop/hdfs/*:/export/server/hadoop/share/hadoop/mapreduce/*:/export/server/hadoop/share/hadoop/yarn:/export/server/hadoop/share/hadoop/yarn/lib/*:/export/server/hadoop/share/hadoop/yarn/*:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-core-4.5.10.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-kms-2.11.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-java-sdk-ram-3.1.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aliyun-sdk-oss-3.13.0.jar:/export/server/hadoop/share/hadoop/tools/lib/aws-java-sdk-bundle-1.12.367.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-data-lake-store-sdk-2.3.9.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-keyvault-core-1.0.0.jar:/export/server/hadoop/share/hadoop/tools/lib/azure-storage-7.0.1.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aliyun-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archive-logs-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-archives-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-aws-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-azure-datalake-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-client-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-datajoin-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-distcp-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-blockgen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-infra-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-dynamometer-workload-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-extras-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-fs2img-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-gridmix-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-kafka-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-minicluster-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-resourceestimator-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-rumen-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-sls-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hadoop-streaming-3.3.6.jar:/export/server/hadoop/share/hadoop/tools/lib/hamcrest-core-1.3.jar:/export/server/hadoop/share/hadoop/tools/lib/ini4j-0.5.4.jar:/export/server/hadoop/share/hadoop/tools/lib/jdk.tools-1.8.jar:/export/server/hadoop/share/hadoop/tools/lib/jdom2-2.0.6.jar:/export/server/hadoop/share/hadoop/tools/lib/junit-4.13.2.jar:/export/server/hadoop/share/hadoop/tools/lib/kafka-clients-2.8.2.jar:/export/server/hadoop/share/hadoop/tools/lib/lz4-java-1.7.1.jar:/export/server/hadoop/share/hadoop/tools/lib/ojalgo-43.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-api-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-noop-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/opentracing-util-0.33.0.jar:/export/server/hadoop/share/hadoop/tools/lib/org.jacoco.agent-0.8.5-runtime.jar:/export/server/hadoop/share/hadoop/tools/lib/wildfly-openssl-1.1.3.Final.jar:/export/server/hadoop/share/hadoop/tools/lib/zstd-jni-1.4.9-1.jar:/export/server/hbase/conf:/export/server/hive/lib/*' -Djava.library.path=:/export/server/hadoop/lib/native:/export/server/hadoop/lib/native org.apache.flume.node.Application --conf-file flume_config.conf --name agent
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/apache-flume-1.11.0-bin/lib/log4j-slf4j-impl-2.18.0.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/apache-hive-3.1.3-bin/lib/log4j-slf4j-impl-2.17.1.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.apache.logging.slf4j.Log4jLoggerFactory]
2025-06-13T01:05:19,394 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,405 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,406 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T01:05:19,406 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,406 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T01:05:19,407 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,407 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T01:05:19,407 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,407 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:kafka-source
2025-06-13T01:05:19,408 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,410 INFO  [main] conf.FlumeConfiguration: Processing:memory-channel
2025-06-13T01:05:19,410 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,410 INFO  [main] conf.FlumeConfiguration: Added sinks: hdfs-sink Agent: agent
2025-06-13T01:05:19,411 INFO  [main] conf.FlumeConfiguration: Processing:hdfs-sink
2025-06-13T01:05:19,411 WARN  [main] conf.FlumeConfiguration: Agent configuration for 'agent' has no configfilters.
2025-06-13T01:05:19,455 INFO  [main] conf.FlumeConfiguration: Post-validation flume configuration contains configuration for agents: [agent]
2025-06-13T01:05:19,457 INFO  [main] node.AbstractConfigurationProvider: Creating channels
2025-06-13T01:05:19,466 INFO  [main] channel.DefaultChannelFactory: Creating instance of channel memory-channel type memory
2025-06-13T01:05:19,472 INFO  [main] node.AbstractConfigurationProvider: Created channel memory-channel
2025-06-13T01:05:19,473 INFO  [main] source.DefaultSourceFactory: Creating instance of source kafka-source, type org.apache.flume.source.kafka.KafkaSource
2025-06-13T01:05:19,526 INFO  [main] sink.DefaultSinkFactory: Creating instance of sink: hdfs-sink, type: hdfs
2025-06-13T01:05:19,542 INFO  [main] node.AbstractConfigurationProvider: Channel memory-channel connected to [kafka-source, hdfs-sink]
2025-06-13T01:05:19,543 INFO  [main] node.Application: Initializing components
2025-06-13T01:05:19,543 INFO  [main] node.Application: Starting new configuration:{ sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE} counterGroup:{ name:null counters:{} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T01:05:19,544 INFO  [main] node.Application: Starting Channel memory-channel
2025-06-13T01:05:19,549 INFO  [main] node.Application: Waiting for channel: memory-channel to start. Sleeping for 500 ms
2025-06-13T01:05:19,552 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: CHANNEL, name: memory-channel: Successfully registered new MBean.
2025-06-13T01:05:19,553 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: CHANNEL, name: memory-channel started
2025-06-13T01:05:20,051 INFO  [main] node.Application: Starting Sink hdfs-sink
2025-06-13T01:05:20,053 INFO  [main] node.Application: Starting Source kafka-source
2025-06-13T01:05:20,055 INFO  [lifecycleSupervisor-1-1] kafka.KafkaSource: Starting org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:IDLE}...
2025-06-13T01:05:20,055 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SINK, name: hdfs-sink: Successfully registered new MBean.
2025-06-13T01:05:20,055 INFO  [lifecycleSupervisor-1-0] instrumentation.MonitoredCounterGroup: Component type: SINK, name: hdfs-sink started
2025-06-13T01:05:20,176 INFO  [lifecycleSupervisor-1-1] consumer.ConsumerConfig: 	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism = GSSAPI
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 10000
	socket.connection.setup.timeout.max.ms = 127000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.ByteArrayDeserializer

2025-06-13T01:05:20,455 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka version: 2.7.2
2025-06-13T01:05:20,456 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka commitId: 37a1cc36bf4d76f3
2025-06-13T01:05:20,456 INFO  [lifecycleSupervisor-1-1] utils.AppInfoParser: Kafka startTimeMs: 1749747920450
2025-06-13T01:05:20,461 INFO  [lifecycleSupervisor-1-1] consumer.KafkaConsumer: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Subscribed to topic(s): attendance
2025-06-13T01:05:20,461 INFO  [lifecycleSupervisor-1-1] kafka.KafkaSource: Kafka source kafka-source started.
2025-06-13T01:05:20,462 INFO  [lifecycleSupervisor-1-1] instrumentation.MonitoredCounterGroup: Monitored counter group for type: SOURCE, name: kafka-source: Successfully registered new MBean.
2025-06-13T01:05:20,462 INFO  [lifecycleSupervisor-1-1] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source started
2025-06-13T01:05:21,503 INFO  [PollableSourceRunner-KafkaSource-kafka-source] clients.Metadata: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Cluster ID: tl7xkvfJTiWgePUCLciJrg
2025-06-13T01:05:22,507 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Discovered group coordinator node1:9092 (id: 2147483647 rack: null)
2025-06-13T01:05:22,510 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T01:05:22,526 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] (Re-)joining group
2025-06-13T01:05:22,532 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully joined group with generation Generation{generationId=1, memberId='consumer-offline_group_b-1-e2e850db-0da2-472c-9ec1-4566da6c69b5', protocol='range'}
2025-06-13T01:05:22,535 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Finished assignment for group at generation 1: {consumer-offline_group_b-1-e2e850db-0da2-472c-9ec1-4566da6c69b5=Assignment(partitions=[attendance-0])}
2025-06-13T01:05:22,548 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Successfully synced group in generation Generation{generationId=1, memberId='consumer-offline_group_b-1-e2e850db-0da2-472c-9ec1-4566da6c69b5', protocol='range'}
2025-06-13T01:05:22,550 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Notifying assignor about the new Assignment(partitions=[attendance-0])
2025-06-13T01:05:22,555 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Adding newly assigned partitions: attendance-0
2025-06-13T01:05:22,555 INFO  [PollableSourceRunner-KafkaSource-kafka-source] kafka.SourceRebalanceListener: topic attendance - partition 0 assigned.
2025-06-13T01:05:22,569 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Found no committed offset for partition attendance-0
2025-06-13T01:05:22,612 INFO  [PollableSourceRunner-KafkaSource-kafka-source] internals.SubscriptionState: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Resetting offset for partition attendance-0 to position FetchPosition{offset=13796, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[node1:9092 (id: 0 rack: null)], epoch=0}}.
2025-06-13T01:05:26,546 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:05:26,741 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749747926547.txt.tmp
2025-06-13T01:10:30,524 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:10:30,527 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749747926547.txt.tmp
2025-06-13T01:10:30,632 INFO  [hdfs-hdfs-sink-call-runner-3] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749747926547.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749747926547.txt
2025-06-13T01:10:34,469 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:10:34,501 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749748234470.txt.tmp
2025-06-13T01:15:34,550 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:15:34,554 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749748234470.txt.tmp
2025-06-13T01:15:34,571 INFO  [hdfs-hdfs-sink-call-runner-4] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749748234470.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749748234470.txt
2025-06-13T01:15:41,223 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:15:41,273 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749748541223.txt.tmp
2025-06-13T01:20:41,652 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:20:41,652 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749748541223.txt.tmp
2025-06-13T01:20:41,683 INFO  [hdfs-hdfs-sink-call-runner-5] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749748541223.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749748541223.txt
2025-06-13T01:20:44,087 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:20:44,136 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749748844088.txt.tmp
2025-06-13T01:25:44,177 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:25:44,183 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749748844088.txt.tmp
2025-06-13T01:25:44,209 INFO  [hdfs-hdfs-sink-call-runner-6] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749748844088.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749748844088.txt
2025-06-13T01:25:50,806 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:25:50,850 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749749150806.txt.tmp
2025-06-13T01:30:50,890 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:30:50,897 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749749150806.txt.tmp
2025-06-13T01:30:50,926 INFO  [hdfs-hdfs-sink-call-runner-8] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749749150806.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749749150806.txt
2025-06-13T01:30:57,419 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:30:57,443 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749749457420.txt.tmp
2025-06-13T01:35:57,503 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:35:57,509 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749749457420.txt.tmp
2025-06-13T01:35:57,540 INFO  [hdfs-hdfs-sink-call-runner-7] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749749457420.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749749457420.txt
2025-06-13T01:36:00,079 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:36:00,116 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749749760080.txt.tmp
2025-06-13T01:41:00,154 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:41:00,159 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749749760080.txt.tmp
2025-06-13T01:41:00,183 INFO  [hdfs-hdfs-sink-call-runner-4] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749749760080.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749749760080.txt
2025-06-13T01:41:00,693 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:41:00,733 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749750060694.txt.tmp
2025-06-13T01:46:00,769 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:46:00,777 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749750060694.txt.tmp
2025-06-13T01:46:00,804 INFO  [hdfs-hdfs-sink-call-runner-2] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749750060694.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749750060694.txt
2025-06-13T01:46:01,317 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:46:01,359 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749750361318.txt.tmp
2025-06-13T01:51:01,393 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:51:01,396 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749750361318.txt.tmp
2025-06-13T01:51:01,414 INFO  [hdfs-hdfs-sink-call-runner-1] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749750361318.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749750361318.txt
2025-06-13T01:51:01,866 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:51:01,888 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749750661867.txt.tmp
2025-06-13T01:56:01,926 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T01:56:01,929 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749750661867.txt.tmp
2025-06-13T01:56:01,950 INFO  [hdfs-hdfs-sink-call-runner-9] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749750661867.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749750661867.txt
2025-06-13T01:56:02,540 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T01:56:02,630 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749750962541.txt.tmp
2025-06-13T02:01:02,743 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.HDFSEventSink: Writer callback called.
2025-06-13T02:01:02,750 INFO  [hdfs-hdfs-sink-roll-timer-0] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749750962541.txt.tmp
2025-06-13T02:01:02,771 INFO  [hdfs-hdfs-sink-call-runner-9] hdfs.BucketWriter: Renaming /home/<USER>/attendance/2025/06/13/attendance-data.1749750962541.txt.tmp to /home/<USER>/attendance/2025/06/13/attendance-data.1749750962541.txt
2025-06-13T02:01:03,041 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSDataStream: Serializer = TEXT, UseRawLocalFileSystem = false
2025-06-13T02:01:03,097 INFO  [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.BucketWriter: Creating /home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp
2025-06-13T02:03:48,378 INFO  [agent-shutdown-hook] node.Application: Shutting down configuration: { sourceRunners:{kafka-source=PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:START} counterGroup:{ name:null counters:{runner.backoffs.consecutive=0, runner.polls=1881, runner.backoffs=1159} } }} sinkRunners:{hdfs-sink=SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.backoffs=219} } }} channels:{memory-channel=org.apache.flume.channel.MemoryChannel{name: memory-channel}} }
2025-06-13T02:03:48,379 INFO  [agent-shutdown-hook] node.Application: Stopping Source kafka-source
2025-06-13T02:03:48,379 INFO  [agent-shutdown-hook] lifecycle.LifecycleSupervisor: Stopping component: PollableSourceRunner: { source:org.apache.flume.source.kafka.KafkaSource{name:kafka-source,state:START} counterGroup:{ name:null counters:{runner.backoffs.consecutive=0, runner.polls=1881, runner.backoffs=1159} } }
2025-06-13T02:03:48,394 ERROR [PollableSourceRunner-KafkaSource-kafka-source] kafka.KafkaSource: KafkaSource EXCEPTION, {}
org.apache.kafka.common.errors.InterruptException: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1292) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1233) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1206) ~[kafka-clients-2.7.2.jar:?]
	at org.apache.flume.source.kafka.KafkaSource.doProcess(KafkaSource.java:247) ~[flume-kafka-source-1.11.0.jar:1.11.0]
	at org.apache.flume.source.AbstractPollableSource.process(AbstractPollableSource.java:60) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.source.PollableSourceRunner$PollingRunner.run(PollableSourceRunner.java:133) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.InterruptedException
	... 10 more
2025-06-13T02:03:48,438 INFO  [PollableSourceRunner-KafkaSource-kafka-source] source.PollableSourceRunner: Source runner interrupted. Exiting
2025-06-13T02:03:48,447 INFO  [agent-shutdown-hook] internals.ConsumerCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Revoke previously assigned partitions attendance-0
2025-06-13T02:03:48,448 INFO  [agent-shutdown-hook] kafka.SourceRebalanceListener: topic attendance - partition 0 revoked.
2025-06-13T02:03:48,448 INFO  [agent-shutdown-hook] internals.AbstractCoordinator: [Consumer clientId=consumer-offline_group_b-1, groupId=offline_group_b] Member consumer-offline_group_b-1-e2e850db-0da2-472c-9ec1-4566da6c69b5 sending LeaveGroup request to coordinator node1:9092 (id: 2147483647 rack: null) due to the consumer is being closed
2025-06-13T02:03:48,459 INFO  [agent-shutdown-hook] metrics.Metrics: Metrics scheduler closed
2025-06-13T02:03:48,461 INFO  [agent-shutdown-hook] metrics.Metrics: Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-13T02:03:48,461 INFO  [agent-shutdown-hook] metrics.Metrics: Metrics reporters closed
2025-06-13T02:03:48,540 INFO  [agent-shutdown-hook] utils.AppInfoParser: App info kafka.consumer for consumer-offline_group_b-1 unregistered
2025-06-13T02:03:48,541 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Component type: SOURCE, name: kafka-source stopped
2025-06-13T02:03:48,541 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.start.time == 1749747920462
2025-06-13T02:03:48,541 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.stop.time == 1749751428540
2025-06-13T02:03:48,541 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.commit.time == 3325
2025-06-13T02:03:48,541 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.empty.count == 1873
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. source.kafka.event.get.time == 721718
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append-batch.accepted == 0
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append-batch.received == 0
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append.accepted == 0
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.append.received == 0
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.channel.write.fail == 0
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.event.read.fail == 1
2025-06-13T02:03:48,542 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.events.accepted == 826
2025-06-13T02:03:48,546 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.events.received == 826
2025-06-13T02:03:48,546 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.generic.processing.fail == 0
2025-06-13T02:03:48,546 INFO  [agent-shutdown-hook] instrumentation.MonitoredCounterGroup: Shutdown Metric for type: SOURCE, name: kafka-source. src.open-connection.count == 0
2025-06-13T02:03:48,547 INFO  [agent-shutdown-hook] kafka.KafkaSource: Kafka Source kafka-source stopped. Metrics: SOURCE:kafka-source{src.events.accepted=826, src.open-connection.count=0, src.append.received=0, src.channel.write.fail=0, source.kafka.event.get.time=721718, source.kafka.empty.count=1873, src.append-batch.accepted=0, src.event.read.fail=1, src.append-batch.received=0, src.generic.processing.fail=0, src.append.accepted=0, src.events.received=826, source.kafka.commit.time=3325}
2025-06-13T02:03:48,547 INFO  [agent-shutdown-hook] node.Application: Stopping Sink hdfs-sink
2025-06-13T02:03:48,547 INFO  [agent-shutdown-hook] lifecycle.LifecycleSupervisor: Stopping component: SinkRunner: { policy:org.apache.flume.sink.DefaultSinkProcessor@1603cd68 counterGroup:{ name:null counters:{runner.backoffs.consecutive=1, runner.backoffs=219} } }
2025-06-13T02:03:48,559 ERROR [SinkRunner-PollingRunner-DefaultSinkProcessor] hdfs.HDFSEventSink: process failed
java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.BucketWriter.checkAndThrowInterruptedException(BucketWriter.java:708) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.flush(BucketWriter.java:477) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:439) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.DefaultSinkProcessor.process(DefaultSinkProcessor.java:39) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.SinkRunner$PollingRunner.run(SinkRunner.java:145) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
2025-06-13T02:03:48,560 ERROR [SinkRunner-PollingRunner-DefaultSinkProcessor] flume.SinkRunner: Unable to deliver event. Exception follows.
org.apache.flume.EventDeliveryException: java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:462) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.DefaultSinkProcessor.process(DefaultSinkProcessor.java:39) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.SinkRunner$PollingRunner.run(SinkRunner.java:145) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.lang.InterruptedException: Timed out before HDFS call was made. Your hdfs.callTimeout might be set too low or HDFS calls are taking too long.
	at org.apache.flume.sink.hdfs.BucketWriter.checkAndThrowInterruptedException(BucketWriter.java:708) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.flush(BucketWriter.java:477) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.HDFSEventSink.process(HDFSEventSink.java:439) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	... 3 more
2025-06-13T02:03:48,863 WARN  [ResponseProcessor for block BP-1911512959-*********-1747576318841:blk_1076513047_2772292] hdfs.DataStreamer: Exception for BP-1911512959-*********-1747576318841:blk_1076513047_2772292
java.io.EOFException: Unexpected EOF while trying to read response from server
	at org.apache.hadoop.hdfs.protocolPB.PBHelperClient.vintPrefixed(PBHelperClient.java:521) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.protocol.datatransfer.PipelineAck.readFields(PipelineAck.java:213) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer$ResponseProcessor.run(DataStreamer.java:1137) ~[hadoop-hdfs-client-3.3.6.jar:?]
2025-06-13T02:03:53,561 INFO  [agent-shutdown-hook] hdfs.HDFSEventSink: Closing /home/<USER>/attendance/2025/06/13/attendance-data
2025-06-13T02:03:53,562 ERROR [hdfs-hdfs-sink-call-runner-1] hdfs.AbstractHDFSWriter: Error while trying to hflushOrSync!
2025-06-13T02:03:53,562 WARN  [agent-shutdown-hook] hdfs.BucketWriter: pre-close flush failed
java.io.IOException: All datanodes [DatanodeInfoWithStorage[127.0.0.1:9866,DS-33b7690f-0d54-4c03-afc0-181817ce0712,DISK]] are bad. Aborting...
	at org.apache.hadoop.hdfs.DataStreamer.handleBadDatanode(DataStreamer.java:1609) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.setupPipelineInternal(DataStreamer.java:1543) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.setupPipelineForAppendOrRecovery(DataStreamer.java:1529) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.processDatanodeOrExternalError(DataStreamer.java:1305) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.run(DataStreamer.java:668) ~[hadoop-hdfs-client-3.3.6.jar:?]
2025-06-13T02:03:53,564 INFO  [agent-shutdown-hook] hdfs.BucketWriter: Closing /home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp
2025-06-13T02:03:53,564 ERROR [hdfs-hdfs-sink-call-runner-2] hdfs.AbstractHDFSWriter: Error while trying to hflushOrSync!
2025-06-13T02:03:53,564 WARN  [agent-shutdown-hook] hdfs.BucketWriter: Closing file: /home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp failed. Will retry again in 180 seconds.
java.io.IOException: All datanodes [DatanodeInfoWithStorage[127.0.0.1:9866,DS-33b7690f-0d54-4c03-afc0-181817ce0712,DISK]] are bad. Aborting...
	at org.apache.hadoop.hdfs.DataStreamer.handleBadDatanode(DataStreamer.java:1609) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.setupPipelineInternal(DataStreamer.java:1543) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.setupPipelineForAppendOrRecovery(DataStreamer.java:1529) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.processDatanodeOrExternalError(DataStreamer.java:1305) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DataStreamer.run(DataStreamer.java:668) ~[hadoop-hdfs-client-3.3.6.jar:?]
2025-06-13T02:03:53,565 WARN  [agent-shutdown-hook] hdfs.BucketWriter: Unsuccessfully attempted to close /home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp 2147483647 times. Initializing lease recovery.
2025-06-13T02:03:53,581 WARN  [agent-shutdown-hook] hdfs.BucketWriter: Lease recovery failed for /home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.recoverLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.recoverLease(ClientNamenodeProtocolTranslatorPB.java:762) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.recoverLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.recoverLease(DFSClient.java:918) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem$3.doCall(DistributedFileSystem.java:318) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem$3.doCall(DistributedFileSystem.java:315) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.fs.FileSystemLinkResolver.resolve(FileSystemLinkResolver.java:81) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem.recoverLease(DistributedFileSystem.java:330) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.flume.sink.hdfs.BucketWriter.recoverLease(BucketWriter.java:402) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.access$1400(BucketWriter.java:60) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter$CloseHandler.close(BucketWriter.java:361) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.doClose(BucketWriter.java:440) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter.close(BucketWriter.java:426) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.HDFSEventSink.stop(HDFSEventSink.java:495) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.AbstractSingleSinkProcessor.stop(AbstractSingleSinkProcessor.java:44) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.SinkRunner.stop(SinkRunner.java:113) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.lifecycle.LifecycleSupervisor.unsupervise(LifecycleSupervisor.java:170) ~[flume-ng-core-1.11.0.jar:1.11.0]
	at org.apache.flume.node.Application.stopAllComponents(Application.java:155) ~[flume-ng-node-1.11.0.jar:1.11.0]
	at org.apache.flume.node.Application.stop(Application.java:128) ~[flume-ng-node-1.11.0.jar:1.11.0]
	at org.apache.flume.node.Application$1.run(Application.java:501) ~[flume-ng-node-1.11.0.jar:1.11.0]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 32 more
2025-06-13T02:03:53,602 WARN  [agent-shutdown-hook] hdfs.BucketWriter: failed to rename() file (/home/<USER>/attendance/2025/06/13/attendance-data.1749751263042.txt.tmp). Exception follows.
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.getFileInfo(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.getFileInfo(ClientNamenodeProtocolTranslatorPB.java:966) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.getFileInfo(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.getFileInfo(DFSClient.java:1739) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem$29.doCall(DistributedFileSystem.java:1829) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem$29.doCall(DistributedFileSystem.java:1826) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.fs.FileSystemLinkResolver.resolve(FileSystemLinkResolver.java:81) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.DistributedFileSystem.getFileStatus(DistributedFileSystem.java:1841) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.fs.FileSystem.exists(FileSystem.java:1862) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.flume.sink.hdfs.BucketWriter$7.call(BucketWriter.java:680) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter$7.call(BucketWriter.java:677) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter$8$1.run(BucketWriter.java:727) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at org.apache.flume.auth.SimpleAuthenticator.execute(SimpleAuthenticator.java:50) ~[flume-ng-auth-1.11.0.jar:1.11.0]
	at org.apache.flume.sink.hdfs.BucketWriter$8.call(BucketWriter.java:724) ~[flume-hdfs-sink-1.11.0.jar:1.11.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_452]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_452]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 30 more
2025-06-13T02:04:04,823 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 30 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:05,854 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 31 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:06,877 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 32 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:07,886 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 33 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:08,893 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 34 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:09,919 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 35 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:10,951 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 36 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:11,964 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 37 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:12,969 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 38 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:13,990 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 39 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:15,028 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 40 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:16,037 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 41 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:17,054 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 42 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:18,072 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 43 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:19,116 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 44 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:20,145 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 45 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:21,213 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 46 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:22,228 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 47 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:23,272 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 48 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:24,314 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 49 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:25,365 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 50 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:26,386 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 51 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:27,431 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 52 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:28,450 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 53 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:29,492 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 54 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:30,511 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 55 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:31,560 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 56 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:32,589 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 57 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:33,628 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 58 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:34,655 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 59 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:35,684 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 60 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:36,733 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 61 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:37,747 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 62 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:38,802 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 64 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:39,815 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 65 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:40,851 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 66 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:41,872 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 67 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:42,886 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 68 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:43,937 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 69 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:44,974 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 70 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:46,018 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 71 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:47,034 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 72 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:48,076 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 73 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:49,087 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 74 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:50,109 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 75 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:51,153 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 76 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:52,171 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 77 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:53,224 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 78 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:54,248 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 79 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:55,298 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 80 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:56,346 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 81 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:57,371 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 82 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:58,412 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 83 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:04:59,429 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 84 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:00,447 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 85 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:01,489 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 86 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:02,507 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 87 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:03,554 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 88 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:04,572 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 89 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:05,609 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 90 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:06,627 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 91 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:07,640 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 92 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:08,682 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 93 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:09,710 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 94 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:10,762 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 95 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:11,778 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 96 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:12,812 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 98 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:13,851 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 99 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:14,873 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 100 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:15,922 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 101 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:16,948 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 102 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
2025-06-13T02:05:17,979 WARN  [LeaseRenewer:root@node1:8020] impl.LeaseRenewer: Failed to renew lease for [DFSClient_NONMAPREDUCE_571322163_35] for 103 seconds.  Will retry shortly ...
java.net.ConnectException: Call From node1/127.0.0.1 to node1:8020 failed on connection exception: java.net.ConnectException: 连接被拒绝; For more details see:  http://wiki.apache.org/hadoop/ConnectionRefused
	at sun.reflect.GeneratedConstructorAccessor14.newInstance(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:1.8.0_452]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[?:1.8.0_452]
	at org.apache.hadoop.net.NetUtils.wrapWithMessage(NetUtils.java:930) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.wrapException(NetUtils.java:845) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getRpcResponse(Client.java:1571) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1513) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1410) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:258) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.ProtobufRpcEngine2$Invoker.invoke(ProtobufRpcEngine2.java:139) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy27.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.protocolPB.ClientNamenodeProtocolTranslatorPB.renewLease(ClientNamenodeProtocolTranslatorPB.java:749) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at sun.reflect.GeneratedMethodAccessor5.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invokeMethod(RetryInvocationHandler.java:433) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeMethod(RetryInvocationHandler.java:166) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invoke(RetryInvocationHandler.java:158) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler$Call.invokeOnce(RetryInvocationHandler.java:96) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.io.retry.RetryInvocationHandler.invoke(RetryInvocationHandler.java:362) ~[hadoop-common-3.3.6.jar:?]
	at com.sun.proxy.$Proxy28.renewLease(Unknown Source) ~[?:?]
	at org.apache.hadoop.hdfs.DFSClient.renewLease(DFSClient.java:596) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.renew(LeaseRenewer.java:425) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.run(LeaseRenewer.java:445) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer.access$800(LeaseRenewer.java:77) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at org.apache.hadoop.hdfs.client.impl.LeaseRenewer$1.run(LeaseRenewer.java:336) ~[hadoop-hdfs-client-3.3.6.jar:?]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
Caused by: java.net.ConnectException: 连接被拒绝
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method) ~[?:1.8.0_452]
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716) ~[?:1.8.0_452]
	at org.apache.hadoop.net.SocketIOWithTimeout.connect(SocketIOWithTimeout.java:205) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.net.NetUtils.connect(NetUtils.java:600) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupConnection(Client.java:652) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.setupIOstreams(Client.java:773) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client$Connection.access$3800(Client.java:347) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.getConnection(Client.java:1632) ~[hadoop-common-3.3.6.jar:?]
	at org.apache.hadoop.ipc.Client.call(Client.java:1457) ~[hadoop-common-3.3.6.jar:?]
	... 20 more
