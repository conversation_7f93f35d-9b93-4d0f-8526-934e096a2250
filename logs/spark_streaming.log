SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 16:11:59,984 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 16:12:05,613 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:12:09,635 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-4ca36ed8-0e05-4364-90cb-4d2a2f236a1b. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:12:09,636 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:12:12,282 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:12:12,638 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-c5472b1c-c2e8-494d-938f-20b61cd132dd. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:12:12,638 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:12:12,752 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:12:12,952 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-3de8086c-4786-484c-a15f-76b4825c39b5. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:12:12,952 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:12:13,040 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:12:13,293 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-e8335778-7666-4286-b1ee-7aa84db5ca52. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:12:13,293 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:12:13,768 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:12:17,890 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 5545 milliseconds

[Stage 1:>                                                          (0 + 1) / 1]

[Stage 1:>                  (0 + 1) / 1][Stage 3:>               (0 + 23) / 200]

[Stage 1:>                  (0 + 1) / 1][Stage 3:>               (0 + 29) / 200]

[Stage 1:>                  (0 + 1) / 1][Stage 3:=>             (17 + 28) / 200]

[Stage 1:===================(1 + 0) / 1][Stage 3:==>            (29 + 25) / 200]

                                                                                
2025-06-13 16:12:23,520 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 10764 milliseconds

[Stage 3:=============>                                         (50 + 26) / 200]

[Stage 3:===================>                                   (71 + 25) / 200]

[Stage 3:====================>                                  (76 + 24) / 200]

[Stage 3:=========================>                             (91 + 24) / 200]

[Stage 3:===========================>                          (102 + 23) / 200]

[Stage 3:===============================>                      (118 + 24) / 200]

[Stage 3:=================================>                    (123 + 24) / 200]

[Stage 3:==================================>                   (126 + 25) / 200]

[Stage 3:=======================================>              (147 + 24) / 200]

[Stage 3:==========================================>           (156 + 24) / 200]

[Stage 3:=============================================>        (169 + 24) / 200]

[Stage 3:===============================================>      (176 + 24) / 200]

[Stage 3:=================================================>    (185 + 15) / 200]

[Stage 3:====================================================>  (192 + 8) / 200]

[Stage 3:======================================================>(197 + 3) / 200]

                                                                                
2025-06-13 16:12:27,086 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 14042 milliseconds

[Stage 4:>                                                          (0 + 1) / 1]

[Stage 4:>                  (0 + 1) / 1][Stage 5:>                  (0 + 1) / 1]

[Stage 4:>    (0 + 1) / 1][Stage 5:>    (0 + 1) / 1][Stage 7:>(21 + 22) / 200]

                                                                                
2025-06-13 16:12:28,842 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 10951 milliseconds

[Stage 7:===================>                                   (70 + 25) / 200]

[Stage 7:======================>                                (82 + 24) / 200]

[Stage 7:=========================>                             (91 + 25) / 200]

[Stage 7:============================>                         (104 + 24) / 200]

[Stage 7:=================================>                    (124 + 24) / 200]

[Stage 7:======================================>               (143 + 24) / 200]

[Stage 7:==========================================>           (158 + 24) / 200]

[Stage 7:================================================>     (180 + 20) / 200]

[Stage 7:=============>(190 + 10) / 200][Stage 8:>                  (0 + 1) / 1]

[Stage 7:==============>(199 + 1) / 200][Stage 8:>                  (0 + 1) / 1]

                                                                                
2025-06-13 16:12:31,375 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 17601 milliseconds

[Stage 9:>                                                          (0 + 1) / 1]

                                                                                

[Stage 10:>                                                         (0 + 1) / 1]

[Stage 10:>                 (0 + 1) / 1][Stage 11:>                 (0 + 1) / 1]

[Stage 10:>                 (0 + 1) / 1][Stage 11:==================(1 + 0) / 1]

                                                                                

[Stage 12:========>                                             (30 + 24) / 200]

[Stage 12:================>                                     (62 + 24) / 200]

[Stage 12:=========================>                            (94 + 25) / 200]

[Stage 12:===============================>                     (117 + 24) / 200]

[Stage 12:=======================================>             (149 + 24) / 200]

[Stage 12:===============================================>     (181 + 19) / 200]

[Stage 12:===================================================>  (191 + 9) / 200]

                                                                                

[Stage 13:>                                                         (0 + 1) / 1]

                                                                                

[Stage 14:================================>                    (122 + 24) / 200]

[Stage 14:=========================================>           (155 + 24) / 200]

[Stage 14:================================================>    (183 + 17) / 200]

[Stage 14:====================================================> (193 + 7) / 200]

                                                                                
