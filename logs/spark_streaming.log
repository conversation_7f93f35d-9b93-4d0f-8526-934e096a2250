SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 15:00:05,849 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 15:00:10,670 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:14,395 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-82481845-a75d-4419-9cd0-f6dc53c0bfe4. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:14,396 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,108 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,334 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-3ed161d4-b33c-47ba-adea-f7049e4f194c. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,334 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,415 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,612 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-0f609c2e-4f32-444f-8ee6-d0b5708f349d. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,613 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,673 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,832 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-e616490d-7645-4988-842e-c7f0340423a4. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,889 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,909 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,909 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,932 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,010 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,010 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,082 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,082 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,112 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,112 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,234 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,234 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,315 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,315 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,822 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,822 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,632 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,633 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,650 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,650 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,310 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,310 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,359 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,359 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,850 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,850 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,218 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,219 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,470 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,470 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,571 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,571 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,812 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,813 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,177 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,177 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,529 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,530 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,580 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,580 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,921 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,921 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,135 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,136 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,486 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,486 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,637 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,027 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,028 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,294 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,294 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,695 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,696 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,186 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,186 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,805 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,805 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,854 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,854 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,295 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,296 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,613 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,667 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,667 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,067 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,067 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,465 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,465 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,573 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,573 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,776 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,776 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,227 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,228 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,422 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,422 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,731 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,731 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,436 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,436 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,439 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,439 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,480 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,480 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,739 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,739 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,594 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,459 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,459 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,556 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,556 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,706 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,706 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,750 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,750 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,418 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,418 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,466 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,466 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,814 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,424 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,425 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,623 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,623 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,821 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,821 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,872 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,872 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,582 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,582 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,683 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,684 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,887 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,887 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,541 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,541 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,592 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,644 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,644 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,458 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,458 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,610 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,610 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,655 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,655 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,761 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,761 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,620 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,670 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,670 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,520 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,521 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,626 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,626 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,675 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,675 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,727 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,727 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,584 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,585 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,632 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,737 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,737 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,836 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,837 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,791 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,792 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,645 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,646 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,699 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,699 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,746 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,746 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,799 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,799 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,504 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,758 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,758 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,817 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,817 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,865 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,865 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,314 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,771 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,771 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,930 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,930 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,979 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,979 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:44,523 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:44,524 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:44,982 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:44,982 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,137 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,137 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,190 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,190 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,385 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,385 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,940 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,940 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,992 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,993 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,147 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,148 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,242 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,242 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,953 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,953 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,104 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,104 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,967 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,325 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,325 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,723 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,724 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,874 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,874 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,875 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,875 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,384 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,384 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,630 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,083 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,083 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,587 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,841 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,841 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,042 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,042 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,701 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,701 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,898 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,898 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,950 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,950 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:52,652 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:52,653 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:52,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:52,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,056 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,056 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,060 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,061 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,663 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,663 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,974 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,975 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,017 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,017 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,018 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,018 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,575 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,575 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,926 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,926 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,077 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,077 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,782 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,782 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,882 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,882 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,245 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,245 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,286 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,286 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,303 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,303 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,498 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,498 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,752 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,752 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,107 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,108 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,365 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,365 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,622 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,716 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,716 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,174 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,174 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,328 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,329 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,733 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,734 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,781 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,781 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,084 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,084 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,045 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,045 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,348 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,348 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,801 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,002 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,002 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,404 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,404 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,807 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,807 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,857 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,858 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,159 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,159 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,715 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,715 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,864 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,864 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,066 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,066 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,671 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,671 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,772 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,772 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,973 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,974 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:06,929 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:06,929 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:06,939 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,025 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,027 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,079 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,132 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,133 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,185 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,192 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,235 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,236 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,292 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,298 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,342 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,398 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,408 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,443 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,449 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,502 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,514 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:10,437 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 54288 milliseconds

[Stage 1:>                                                        (0 + 0) / 200]

[Stage 1:>               (0 + 24) / 200][Stage 3:>                  (0 + 0) / 1]

[Stage 1:> (0 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:> (0 + 28) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(26 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(33 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(44 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(60 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(77 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(92 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(102 + 26) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(118 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(133 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(148 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(171 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(186 + 14) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>  (0 + 9) / 200]

[Stage 1:>(194 + 6) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:> (3 + 17) / 200]

[Stage 1:>(197 + 3) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>(12 + 21) / 200]

[Stage 1:>(198 + 2) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>(26 + 23) / 200]

                                                                                
2025-06-13 15:01:17,191 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 60300 milliseconds

[Stage 5:===================>                                   (72 + 24) / 200]

[Stage 5:====================>                                  (76 + 24) / 200]
2025-06-13 15:01:17,939 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 61521 milliseconds

[Stage 5:========================>                              (90 + 24) / 200]

[Stage 5:==========================>                            (98 + 24) / 200]

[Stage 5:============================>                         (105 + 24) / 200]

[Stage 5:===============================>                      (116 + 24) / 200]

[Stage 5:===================================>                  (132 + 24) / 200]

[Stage 5:========================================>             (150 + 24) / 200]

[Stage 5:=============================================>        (169 + 24) / 200]

[Stage 5:==================================================>   (187 + 13) / 200]

[Stage 5:=====================================================> (194 + 6) / 200]

[Stage 5:======================================================>(198 + 2) / 200]

                                                                                
2025-06-13 15:01:20,049 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 63373 milliseconds

[Stage 6:>                                                          (0 + 1) / 1]

                                                                                

[Stage 7:>                                                          (0 + 1) / 1]

[Stage 8:==============>                                        (52 + 24) / 200]

[Stage 8:=========================>                             (91 + 24) / 200]

[Stage 8:===============================>                      (117 + 24) / 200]

[Stage 8:=======================================>              (145 + 24) / 200]

[Stage 8:===============================================>      (175 + 24) / 200]

[Stage 8:=================================================>    (185 + 15) / 200]

[Stage 8:=====================================================> (196 + 4) / 200]

                                                                                

[Stage 10:==============================>                      (116 + 24) / 200]

[Stage 10:==========================================>          (162 + 24) / 200]

[Stage 10:==============================================>      (177 + 23) / 200]

[Stage 10:================================================>    (184 + 16) / 200]

[Stage 10:=====================================================>(199 + 1) / 200]

                                                                                

[Stage 11:>                                                         (0 + 1) / 1]

                                                                                

[Stage 15:>                                                         (0 + 1) / 1]

[Stage 16:===============================>                     (118 + 24) / 200]

[Stage 16:==============================================>      (175 + 24) / 200]

[Stage 16:=================================================>   (187 + 13) / 200]

[Stage 16:====================================================> (196 + 4) / 200]

                                                                                

[Stage 21:===============================>                     (117 + 24) / 200]

[Stage 21:=======================================>             (148 + 24) / 200]

[Stage 21:================================================>    (184 + 16) / 200]

[Stage 21:====================================================> (194 + 6) / 200]

                                                                                

[Stage 22:>                                                         (0 + 1) / 1]

                                                                                

[Stage 24:===========================================>         (166 + 25) / 200]

[Stage 24:=================================================>   (188 + 12) / 200]

[Stage 24:=====================================================>(197 + 3) / 200]

                                                                                

[Stage 27:>                                                         (0 + 1) / 1]

                                                                                

[Stage 28:>                                                         (0 + 1) / 1]

                                                                                

[Stage 30:=====================================>               (140 + 24) / 200]

[Stage 30:===============================================>     (179 + 21) / 200]

[Stage 30:=================================================>   (188 + 12) / 200]

                                                                                

[Stage 35:=================================>                   (127 + 24) / 200]

[Stage 35:===================================>                 (135 + 24) / 200]

[Stage 35:===============================================>     (179 + 21) / 200]

[Stage 35:================================================>    (183 + 17) / 200]

[Stage 35:=====================================================>(198 + 2) / 200]

                                                                                

[Stage 36:>                                                         (0 + 1) / 1]

                                                                                

[Stage 39:=========================>                            (94 + 24) / 200]

[Stage 39:================================>                    (123 + 24) / 200]

[Stage 39:===========================================>         (166 + 25) / 200]

[Stage 39:================================================>    (184 + 16) / 200]

[Stage 39:====================================================> (195 + 5) / 200]

                                                                                

[Stage 42:==========================>                          (100 + 24) / 200]

[Stage 42:=================================>                   (127 + 25) / 200]

[Stage 42:===========================================>         (165 + 24) / 200]

[Stage 42:================================================>    (182 + 18) / 200]

[Stage 42:====================================================> (193 + 7) / 200]

                                                                                

[Stage 48:========================================>            (152 + 24) / 200]

[Stage 48:===============================================>     (180 + 20) / 200]

[Stage 48:====================================================> (193 + 7) / 200]

                                                                                
2025-06-13 15:09:39,476 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,496 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,508 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,540 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,577 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,604 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,609 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,658 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,776 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,826 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:39,885 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:40,153 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:40,205 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:40,205 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:40,254 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,012 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,012 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,065 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,163 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,871 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,971 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:41,977 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:42,121 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:42,878 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:43,037 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:43,081 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:43,133 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:43,787 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:44,039 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:44,196 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:44,290 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:44,798 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:45,197 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:45,202 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:45,347 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:45,955 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:46,059 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:46,202 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:46,357 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:46,914 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:47,169 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:47,268 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:47,369 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:47,824 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:48,179 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:48,331 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:48,333 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:48,730 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:49,189 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:49,347 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:49,391 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:49,889 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:50,347 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:50,353 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:50,398 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:51,097 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:51,254 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:51,556 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:51,561 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:52,208 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:52,366 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:52,770 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:52,770 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:53,270 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:53,477 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:53,730 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:53,881 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:54,386 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:54,485 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:54,740 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:54,792 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:55,446 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:55,496 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:55,848 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:56,000 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:56,403 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:56,656 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:56,909 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:57,011 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:57,364 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:57,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:57,967 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:58,068 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:58,420 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:58,420 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:58,924 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:59,125 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:59,376 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:59,577 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:09:59,879 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:00,334 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:00,389 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:00,787 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:00,789 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:01,397 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:01,444 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:01,896 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:01,995 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:02,353 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:02,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:02,856 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:03,005 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:03,412 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:03,470 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:03,814 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:04,067 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:04,429 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:04,620 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:05,023 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:05,074 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:05,386 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:05,679 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:05,982 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:06,084 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:06,596 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:06,688 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:06,990 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:07,193 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:07,548 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:07,653 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:07,999 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:08,158 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:08,460 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:08,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:08,855 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:09,014 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:09,320 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:09,727 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,019 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,025 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,280 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,837 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,981 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:10,987 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:11,242 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:11,898 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:11,899 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:12,146 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:12,352 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:12,906 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:13,057 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:13,155 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:13,413 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:13,764 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:13,917 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:14,274 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:14,364 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:14,922 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:15,131 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:15,281 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:15,572 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:15,829 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:16,190 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:16,287 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:16,427 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:16,734 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:17,198 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:17,396 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:17,585 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:17,843 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:18,407 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:18,502 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:18,692 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:18,696 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:19,508 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:19,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:19,648 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:19,651 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:20,506 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:20,604 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:20,622 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:20,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node 0 (node1/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:10:21,677 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 195 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:10:21,681 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 300 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:10:21,699 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 246 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,752 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 249 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,782 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 197 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,788 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 302 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,805 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 248 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,857 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 251 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,887 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 199 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,894 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 304 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,911 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 250 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:21,964 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 253 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:10:22,012 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 42011 milliseconds
2025-06-13 15:10:22,024 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 42024 milliseconds
2025-06-13 15:10:22,033 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 42032 milliseconds
2025-06-13 15:10:22,084 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 42084 milliseconds

[Stage 52:>                                                         (0 + 1) / 1]

                                                                                

[Stage 53:>                                                         (0 + 1) / 1]

[Stage 54:===============================================>     (179 + 21) / 200]

[Stage 54:====================================================> (193 + 7) / 200]

[Stage 54:=====================================================>(198 + 2) / 200]

                                                                                

[Stage 56:>                                                         (0 + 1) / 1]

                                                                                
2025-06-13 15:13:50,135 ERROR streaming.MicroBatchExecution: Query [id = 794bab8d-bb8d-44cd-9d45-559eb293a6ff, runId = ff44d061-b197-4035-9201-85c1f401f087] terminated with error
java.lang.IllegalStateException: Set(attendance-2) are gone. Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    . 
Some data may have been lost because they are not available in Kafka any more; either the
 data was aged out by Kafka or the topic may have been deleted before all the data in the
 topic was processed. If you don't want your streaming query to fail on such cases, set the
 source option "failOnDataLoss" to "false".
    
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.reportDataLoss(KafkaMicroBatchStream.scala:209)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1$adapted(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaOffsetReaderConsumer.getOffsetRangesFromResolvedOffsets(KafkaOffsetReaderConsumer.scala:501)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.planInputPartitions(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions$lzycompute(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar(DataSourceV2ScanExecBase.scala:87)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar$(DataSourceV2ScanExecBase.scala:86)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.supportsColumnar(MicroBatchScanExec.scala:29)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2Strategy.apply(DataSourceV2Strategy.scala:120)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$1(QueryPlanner.scala:63)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:489)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.execution.QueryExecution$.createSparkPlan(QueryExecution.scala:391)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$sparkPlan$1(QueryExecution.scala:104)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan$lzycompute(QueryExecution.scala:104)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan(QueryExecution.scala:97)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executedPlan$1(QueryExecution.scala:117)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan$lzycompute(QueryExecution.scala:117)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan(QueryExecution.scala:110)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runBatch$14(MicroBatchExecution.scala:577)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runBatch(MicroBatchExecution.scala:567)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$2(MicroBatchExecution.scala:226)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$1(MicroBatchExecution.scala:194)
	at org.apache.spark.sql.execution.streaming.ProcessingTimeExecutor.execute(TriggerExecutor.scala:57)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runActivatedStream(MicroBatchExecution.scala:188)
	at org.apache.spark.sql.execution.streaming.StreamExecution.$anonfun$runStream$1(StreamExecution.scala:334)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.streaming.StreamExecution.org$apache$spark$sql$execution$streaming$StreamExecution$$runStream(StreamExecution.scala:317)
	at org.apache.spark.sql.execution.streaming.StreamExecution$$anon$1.run(StreamExecution.scala:244)
2025-06-13 15:13:50,135 ERROR streaming.MicroBatchExecution: Query [id = bac7d377-e49f-49f6-abad-7f05ef2f533f, runId = 220c8a9e-0c3e-4dd3-92e6-054550358793] terminated with error
java.lang.IllegalStateException: Set(attendance-1) are gone. Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    . 
Some data may have been lost because they are not available in Kafka any more; either the
 data was aged out by Kafka or the topic may have been deleted before all the data in the
 topic was processed. If you don't want your streaming query to fail on such cases, set the
 source option "failOnDataLoss" to "false".
    
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.reportDataLoss(KafkaMicroBatchStream.scala:209)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1$adapted(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaOffsetReaderConsumer.getOffsetRangesFromResolvedOffsets(KafkaOffsetReaderConsumer.scala:501)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.planInputPartitions(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions$lzycompute(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar(DataSourceV2ScanExecBase.scala:87)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar$(DataSourceV2ScanExecBase.scala:86)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.supportsColumnar(MicroBatchScanExec.scala:29)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2Strategy.apply(DataSourceV2Strategy.scala:120)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$1(QueryPlanner.scala:63)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:489)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.execution.QueryExecution$.createSparkPlan(QueryExecution.scala:391)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$sparkPlan$1(QueryExecution.scala:104)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan$lzycompute(QueryExecution.scala:104)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan(QueryExecution.scala:97)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executedPlan$1(QueryExecution.scala:117)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan$lzycompute(QueryExecution.scala:117)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan(QueryExecution.scala:110)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runBatch$14(MicroBatchExecution.scala:577)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runBatch(MicroBatchExecution.scala:567)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$2(MicroBatchExecution.scala:226)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$1(MicroBatchExecution.scala:194)
	at org.apache.spark.sql.execution.streaming.ProcessingTimeExecutor.execute(TriggerExecutor.scala:57)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runActivatedStream(MicroBatchExecution.scala:188)
	at org.apache.spark.sql.execution.streaming.StreamExecution.$anonfun$runStream$1(StreamExecution.scala:334)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.streaming.StreamExecution.org$apache$spark$sql$execution$streaming$StreamExecution$$runStream(StreamExecution.scala:317)
	at org.apache.spark.sql.execution.streaming.StreamExecution$$anon$1.run(StreamExecution.scala:244)
2025-06-13 15:13:50,137 ERROR streaming.MicroBatchExecution: Query [id = a34ac307-e39d-4f5d-ae05-5148269a7bed, runId = 9d746c96-c734-422d-a35a-1310e80e7d6c] terminated with error
java.lang.IllegalStateException: Set(attendance-0) are gone. Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    . 
Some data may have been lost because they are not available in Kafka any more; either the
 data was aged out by Kafka or the topic may have been deleted before all the data in the
 topic was processed. If you don't want your streaming query to fail on such cases, set the
 source option "failOnDataLoss" to "false".
    
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.reportDataLoss(KafkaMicroBatchStream.scala:209)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.$anonfun$planInputPartitions$1$adapted(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.kafka010.KafkaOffsetReaderConsumer.getOffsetRangesFromResolvedOffsets(KafkaOffsetReaderConsumer.scala:501)
	at org.apache.spark.sql.kafka010.KafkaMicroBatchStream.planInputPartitions(KafkaMicroBatchStream.scala:104)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions$lzycompute(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.partitions(MicroBatchScanExec.scala:44)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar(DataSourceV2ScanExecBase.scala:87)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2ScanExecBase.supportsColumnar$(DataSourceV2ScanExecBase.scala:86)
	at org.apache.spark.sql.execution.datasources.v2.MicroBatchScanExec.supportsColumnar(MicroBatchScanExec.scala:29)
	at org.apache.spark.sql.execution.datasources.v2.DataSourceV2Strategy.apply(DataSourceV2Strategy.scala:120)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$1(QueryPlanner.scala:63)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:489)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$3(QueryPlanner.scala:78)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.$anonfun$foldLeft$1$adapted(TraversableOnce.scala:162)
	at scala.collection.Iterator.foreach(Iterator.scala:941)
	at scala.collection.Iterator.foreach$(Iterator.scala:941)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1429)
	at scala.collection.TraversableOnce.foldLeft(TraversableOnce.scala:162)
	at scala.collection.TraversableOnce.foldLeft$(TraversableOnce.scala:160)
	at scala.collection.AbstractIterator.foldLeft(Iterator.scala:1429)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.$anonfun$plan$2(QueryPlanner.scala:75)
	at scala.collection.Iterator$$anon$11.nextCur(Iterator.scala:484)
	at scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:490)
	at org.apache.spark.sql.catalyst.planning.QueryPlanner.plan(QueryPlanner.scala:93)
	at org.apache.spark.sql.execution.SparkStrategies.plan(SparkStrategies.scala:67)
	at org.apache.spark.sql.execution.QueryExecution$.createSparkPlan(QueryExecution.scala:391)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$sparkPlan$1(QueryExecution.scala:104)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan$lzycompute(QueryExecution.scala:104)
	at org.apache.spark.sql.execution.QueryExecution.sparkPlan(QueryExecution.scala:97)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executedPlan$1(QueryExecution.scala:117)
	at org.apache.spark.sql.catalyst.QueryPlanningTracker.measurePhase(QueryPlanningTracker.scala:111)
	at org.apache.spark.sql.execution.QueryExecution.$anonfun$executePhase$1(QueryExecution.scala:143)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.QueryExecution.executePhase(QueryExecution.scala:143)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan$lzycompute(QueryExecution.scala:117)
	at org.apache.spark.sql.execution.QueryExecution.executedPlan(QueryExecution.scala:110)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runBatch$14(MicroBatchExecution.scala:577)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runBatch(MicroBatchExecution.scala:567)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$2(MicroBatchExecution.scala:226)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$1(MicroBatchExecution.scala:194)
	at org.apache.spark.sql.execution.streaming.ProcessingTimeExecutor.execute(TriggerExecutor.scala:57)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runActivatedStream(MicroBatchExecution.scala:188)
	at org.apache.spark.sql.execution.streaming.StreamExecution.$anonfun$runStream$1(StreamExecution.scala:334)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.streaming.StreamExecution.org$apache$spark$sql$execution$streaming$StreamExecution$$runStream(StreamExecution.scala:317)
	at org.apache.spark.sql.execution.streaming.StreamExecution$$anon$1.run(StreamExecution.scala:244)
启动Spark Streaming处理...
[2025-06-13 15:01:17.309772] 成功写入1条记录到attendance_summary
[2025-06-13 15:04:01.893350] 成功写入1条记录到attendance_summary
[2025-06-13 15:04:13.607662] 成功写入1条记录到class_attendance
[2025-06-13 15:04:22.433598] 成功写入2条记录到class_attendance
[2025-06-13 15:04:26.232176] 成功写入2条记录到raw_attendance_data
[2025-06-13 15:04:30.657379] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:04:40.773267] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:04:40.863422] 成功写入1条记录到attendance_summary
[2025-06-13 15:04:52.713289] 成功写入4条记录到class_attendance
[2025-06-13 15:04:55.355036] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:00.364520] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:05.727432] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:12.360816] 成功写入5条记录到class_attendance
[2025-06-13 15:05:21.676366] 成功写入1条记录到attendance_summary
[2025-06-13 15:05:22.822211] 成功写入5条记录到class_attendance
[2025-06-13 15:05:26.175718] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:30.251557] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:41.195958] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:05:50.831259] 成功写入1条记录到attendance_summary
[2025-06-13 15:05:52.243046] 成功写入5条记录到class_attendance
[2025-06-13 15:05:55.755254] 成功写入1条记录到raw_attendance_data
写入MySQL失败: 1406 (22001): Data too long for column 'attendance_status' at row 1
[2025-06-13 15:06:01.246479] 成功写入1条记录到attendance_summary
[2025-06-13 15:06:12.657235] 成功写入6条记录到class_attendance
[2025-06-13 15:06:21.259830] 成功写入1条记录到attendance_summary
[2025-06-13 15:06:26.152300] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:06:32.420791] 成功写入6条记录到class_attendance
[2025-06-13 15:06:35.759809] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:06:42.853538] 成功写入6条记录到class_attendance
[2025-06-13 15:06:51.146841] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:06:51.240795] 成功写入1条记录到attendance_summary
[2025-06-13 15:07:00.821961] 成功写入1条记录到attendance_summary
[2025-06-13 15:07:01.162249] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:07:12.138989] 成功写入6条记录到class_attendance
[2025-06-13 15:07:20.714180] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:07:20.777524] 成功写入1条记录到attendance_summary
[2025-06-13 15:07:26.130237] 成功写入1条记录到raw_attendance_data
写入MySQL失败: 1406 (22001): Data too long for column 'attendance_status' at row 1
[2025-06-13 15:12:52.479682] 成功写入6条记录到class_attendance
[2025-06-13 15:12:56.160994] 成功写入1条记录到raw_attendance_data
[2025-06-13 15:13:01.290035] 成功写入1条记录到attendance_summary
Traceback (most recent call last):
  File "/root/job14/spark_streaming_consumer.py", line 280, in <module>
    main()
  File "/root/job14/spark_streaming_consumer.py", line 277, in main
    processor.start_streaming()
  File "/root/job14/spark_streaming_consumer.py", line 264, in start_streaming
    query0.awaitTermination()
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/sql/streaming.py", line 101, in awaitTermination
    return self._jsq.awaitTermination()
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/py4j/java_gateway.py", line 1304, in __call__
    return_value = get_return_value(
  File "/root/anaconda3/envs/spark_env/lib/python3.9/site-packages/pyspark/sql/utils.py", line 117, in deco
    raise converted from None
pyspark.sql.utils.StreamingQueryException: Set(attendance-0) are gone. Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    . 
Some data may have been lost because they are not available in Kafka any more; either the
 data was aged out by Kafka or the topic may have been deleted before all the data in the
 topic was processed. If you don't want your streaming query to fail on such cases, set the
 source option "failOnDataLoss" to "false".
    
=== Streaming Query ===
Identifier: [id = a34ac307-e39d-4f5d-ae05-5148269a7bed, runId = 9d746c96-c734-422d-a35a-1310e80e7d6c]
Current Committed Offsets: {KafkaV2[Subscribe[attendance]]: {"attendance":{"0":20}}}
Current Available Offsets: {KafkaV2[Subscribe[attendance]]: {"attendance":{"1":11}}}

Current State: ACTIVE
Thread State: RUNNABLE

Logical Plan:
Project [data_array#24[0] AS class_number#27, data_array#24[1] AS student_name#28, data_array#24[2] AS course_name#29, data_array#24[3] AS student_id#30, cast(data_array#24[4] as int) AS score#31, data_array#24[5] AS attendance_status#32, timestamp#12]
+- Project [split(raw_data#21,  , -1) AS data_array#24, timestamp#12]
   +- Project [cast(value#8 as string) AS raw_data#21, timestamp#12]
      +- StreamingDataSourceV2Relation [key#7, value#8, topic#9, partition#10, offset#11L, timestamp#12, timestampType#13], org.apache.spark.sql.kafka010.KafkaSourceProvider$KafkaScan@65853d9b, KafkaV2[Subscribe[attendance]]

