SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 15:00:05,849 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 15:00:10,670 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:14,395 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-82481845-a75d-4419-9cd0-f6dc53c0bfe4. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:14,396 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,108 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,334 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-3ed161d4-b33c-47ba-adea-f7049e4f194c. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,334 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,415 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,612 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-0f609c2e-4f32-444f-8ee6-d0b5708f349d. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,613 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,673 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,832 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-e616490d-7645-4988-842e-c7f0340423a4. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 15:00:16,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,834 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 15:00:16,889 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 15:00:16,909 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,909 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:16,932 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:16,931 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,010 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,010 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,031 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,082 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,082 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,112 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,112 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,234 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,234 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,285 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,315 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,315 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,689 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:17,822 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:17,822 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,632 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,633 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:18,650 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:18,650 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,310 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,310 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,359 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,359 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,713 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:19,850 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:19,850 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,218 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,219 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,470 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,470 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,571 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,571 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:20,812 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:20,813 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,177 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,177 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,529 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,530 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,580 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,580 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:21,921 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:21,921 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,135 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,136 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,486 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,486 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:22,637 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:22,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,027 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,028 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,294 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,294 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:23,695 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:23,696 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,186 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,186 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,805 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,805 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:24,854 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:24,854 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,295 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,296 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,613 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:25,667 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:25,667 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,067 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,067 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,465 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,465 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,573 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,573 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:26,776 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:26,776 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,227 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,228 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,422 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,422 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:27,731 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:27,731 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,436 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,436 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,439 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,439 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,480 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,480 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:28,739 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:28,739 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,594 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,595 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:29,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:29,639 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,459 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,459 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,556 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,556 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,706 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,706 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:30,750 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:30,750 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,418 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,418 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,466 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,466 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:31,814 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:31,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,424 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,425 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,623 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,623 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,821 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,821 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:32,872 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:32,872 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,582 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,582 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,683 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,684 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:33,887 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:33,887 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,541 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,541 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,592 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,644 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,644 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:34,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:34,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,447 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,448 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,598 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:35,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:35,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,458 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,458 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,610 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,610 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,655 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,655 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:36,761 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:36,761 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,614 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,619 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,620 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:37,670 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:37,670 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,520 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,521 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,626 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,626 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,675 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,675 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:38,727 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:38,727 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,584 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,585 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,632 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,737 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,737 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:39,836 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:39,837 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,591 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,638 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,742 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:40,791 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:40,792 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,645 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,646 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,699 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,699 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,746 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,746 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:41,799 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:41,799 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,503 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,504 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,758 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,758 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,817 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,817 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:42,865 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:42,865 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,314 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,771 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,771 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,930 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,930 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:43,979 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:43,979 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:44,523 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:44,524 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:44,982 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:44,982 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,137 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,137 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,190 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,190 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,385 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,385 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,940 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,940 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:45,992 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:45,993 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,147 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,148 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,242 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,242 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,802 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:46,953 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:46,953 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,104 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,104 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,313 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,815 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:47,966 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:47,967 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,325 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,325 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,723 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,724 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,874 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,874 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:48,875 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:48,875 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,384 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,384 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,630 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,631 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:49,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:49,833 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,083 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,083 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,586 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,587 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:50,841 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:50,841 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,042 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,042 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,492 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,701 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,701 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,898 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,898 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:51,950 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:51,950 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:52,652 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:52,653 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:52,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:52,760 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,056 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,056 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,060 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,061 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,663 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,663 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:53,974 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:53,975 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,017 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,017 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,018 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,018 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,575 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,575 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:54,926 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:54,926 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,077 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,077 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,782 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,782 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:55,882 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:55,882 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,245 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,245 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,286 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,286 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:56,941 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,303 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,303 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,498 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,498 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:57,752 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:57,752 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,107 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,108 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,365 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,365 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,622 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:58,716 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:58,716 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,174 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,174 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,328 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,329 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,733 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,734 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:00:59,781 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:00:59,781 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,084 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,084 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:00,743 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,045 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,045 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,348 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,348 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:01,801 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:01,800 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,002 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,002 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,404 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,404 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,807 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,807 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:02,857 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:02,858 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,159 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,159 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,564 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,715 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,715 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:03,864 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:03,864 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,066 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,066 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,621 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,671 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,671 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,772 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,772 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:04,973 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:04,974 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,831 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Broker may not be available.
2025-06-13 15:01:05,832 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-06-13 15:01:06,929 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:06,929 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:06,939 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,025 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,027 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,079 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,087 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 2 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,132 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,133 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,185 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,192 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 5 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,235 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,236 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,292 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,298 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 7 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,339 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=UNKNOWN_TOPIC_OR_PARTITION}
2025-06-13 15:01:07,342 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,398 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,408 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 9 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,443 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-2, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,449 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-4, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,502 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-1, groupId=realtime_group_a] Error while fetching metadata with correlation id 13 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:07,514 WARN clients.NetworkClient: [Consumer clientId=consumer-realtime_group_a-3, groupId=realtime_group_a] Error while fetching metadata with correlation id 11 : {attendance=LEADER_NOT_AVAILABLE}
2025-06-13 15:01:10,437 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 5000 milliseconds, but spent 54288 milliseconds

[Stage 1:>                                                        (0 + 0) / 200]

[Stage 1:>               (0 + 24) / 200][Stage 3:>                  (0 + 0) / 1]

[Stage 1:> (0 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:> (0 + 28) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(26 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(33 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(44 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(60 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(77 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:>(92 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(102 + 26) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(118 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(133 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(148 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(171 + 24) / 200][Stage 3:>    (0 + 0) / 1][Stage 5:>  (0 + 0) / 200]

[Stage 1:(186 + 14) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>  (0 + 9) / 200]

[Stage 1:>(194 + 6) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:> (3 + 17) / 200]

[Stage 1:>(197 + 3) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>(12 + 21) / 200]

[Stage 1:>(198 + 2) / 200][Stage 3:>    (0 + 1) / 1][Stage 5:>(26 + 23) / 200]

                                                                                
2025-06-13 15:01:17,191 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 60300 milliseconds

[Stage 5:===================>                                   (72 + 24) / 200]

[Stage 5:====================>                                  (76 + 24) / 200]
2025-06-13 15:01:17,939 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 61521 milliseconds

[Stage 5:========================>                              (90 + 24) / 200]

[Stage 5:==========================>                            (98 + 24) / 200]

[Stage 5:============================>                         (105 + 24) / 200]

[Stage 5:===============================>                      (116 + 24) / 200]

[Stage 5:===================================>                  (132 + 24) / 200]

[Stage 5:========================================>             (150 + 24) / 200]

[Stage 5:=============================================>        (169 + 24) / 200]

[Stage 5:==================================================>   (187 + 13) / 200]

[Stage 5:=====================================================> (194 + 6) / 200]
