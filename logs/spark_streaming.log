nohup: 忽略输入
SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 16:00:13,859 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 16:00:18,592 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:00:22,302 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-6a6bb1b1-8c86-4257-bfce-87417ab95c32. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:00:22,304 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:00:23,812 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:00:24,034 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-dc2a1f7f-b938-44ab-b43e-300c8e94062e. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:00:24,034 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:00:24,107 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:00:24,280 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-88e3dc0a-321a-4474-9722-60a4dd3d438c. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:00:24,281 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:00:24,344 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 16:00:24,510 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-986b89ca-efa3-451b-9c46-3350c00fd6de. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 16:00:24,510 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 16:00:24,571 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    

[Stage 1:>                                                          (0 + 0) / 1]

[Stage 1:>                  (0 + 1) / 1][Stage 3:>               (0 + 23) / 200]

[Stage 1:>    (0 + 1) / 1][Stage 3:> (0 + 23) / 200][Stage 5:>  (0 + 0) / 200]

[Stage 1:>    (0 + 1) / 1][Stage 3:> (0 + 29) / 200][Stage 5:>  (0 + 0) / 200]

                                                                                

[Stage 3:====>          (61 + 25) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:=====>         (72 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:======>        (87 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:=======>      (108 + 23) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:========>     (123 + 24) / 200][Stage 5:>                (0 + 0) / 200]

[Stage 3:(145 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(169 + 24) / 200][Stage 5:>  (0 + 0) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:(186 + 14) / 200][Stage 5:> (8 + 11) / 200][Stage 6:>    (0 + 0) / 1]

[Stage 3:>(197 + 3) / 200][Stage 5:>(25 + 21) / 200][Stage 6:>    (0 + 0) / 1]

                                                                                

[Stage 5:=======>       (95 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:========>     (120 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:==========>   (145 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:===========>  (170 + 24) / 200][Stage 6:>                  (0 + 0) / 1]

[Stage 5:=============>(190 + 10) / 200][Stage 6:>                  (0 + 1) / 1]

[Stage 5:==============>(198 + 2) / 200][Stage 6:>                  (0 + 1) / 1]

                                                                                
2025-06-13 16:00:34,515 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 10167 milliseconds

[Stage 6:>                                                          (0 + 1) / 1]

                                                                                

[Stage 7:>                                                          (0 + 1) / 1]

                                                                                

[Stage 8:>                                                          (0 + 1) / 1]

                                                                                

[Stage 14:>                                                         (0 + 1) / 1]

[Stage 15:=====================>                                (78 + 24) / 200]

[Stage 15:=============================>                       (112 + 24) / 200]

[Stage 15:======================================>              (146 + 24) / 200]

[Stage 15:==============================================>      (174 + 24) / 200]

[Stage 15:==================================================>  (189 + 11) / 200]

[Stage 15:=====================================================>(198 + 2) / 200]

                                                                                

[Stage 16:>                                                         (0 + 1) / 1]

[Stage 18:>                                                         (0 + 1) / 1]

                                                                                

[Stage 17:================================================>    (184 + 16) / 200]

[Stage 17:=================================================>   (185 + 15) / 200]

                                                                                

[Stage 19:>                                                         (0 + 1) / 1]

                                                                                

[Stage 22:=====================================================>(199 + 1) / 200]

                                                                                

[Stage 26:>                 (0 + 1) / 1][Stage 27:>                 (0 + 1) / 1]

                                                                                

[Stage 28:=================================>                   (127 + 24) / 200]

[Stage 28:==========================================>          (161 + 24) / 200]

[Stage 28:==============================================>      (174 + 24) / 200]

[Stage 28:====================================================> (194 + 6) / 200]

[Stage 28:=====================================================>(199 + 1) / 200]

                                                                                

[Stage 33:=================>                                    (65 + 24) / 200]

[Stage 33:============================>                        (106 + 24) / 200]

[Stage 33:===============================>                     (118 + 24) / 200]

[Stage 33:=====================================>               (143 + 24) / 200]

[Stage 33:================================================>    (182 + 18) / 200]

[Stage 33:=================================================>   (186 + 14) / 200]

                                                                                

[Stage 36:==========================>                           (97 + 24) / 200]

[Stage 36:==================================>                  (130 + 24) / 200]

[Stage 36:========================================>            (154 + 24) / 200]

[Stage 36:================================================>    (182 + 18) / 200]

[Stage 36:=================================================>   (188 + 12) / 200]

                                                                                

[Stage 38:==============================>                      (114 + 24) / 200]

[Stage 38:==================================>                  (129 + 24) / 200]

[Stage 38:==========================================>          (161 + 24) / 200]

[Stage 38:================================================>    (182 + 18) / 200]

[Stage 38:====================================================> (195 + 5) / 200]

                                                                                

[Stage 39:>                                                         (0 + 1) / 1]

                                                                                
