nohup: 忽略输入
[2025-06-13 11:01:07,207] INFO Registered kafka:type=kafka.Log4jController MBean (kafka.utils.Log4jControllerRegistration$)
[2025-06-13 11:01:08,091] INFO Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation (org.apache.zookeeper.common.X509Util)
[2025-06-13 11:01:08,271] INFO Registered signal handlers for TERM, INT, HUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-06-13 11:01:08,279] INFO starting (kafka.server.KafkaServer)
[2025-06-13 11:01:08,280] INFO Connecting to zookeeper on node1:2181 (kafka.server.KafkaServer)
[2025-06-13 11:01:08,303] INFO [ZooKeeperClient Kafka server] Initializing a new session to node1:2181. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 11:01:08,309] INFO Client environment:zookeeper.version=3.5.9-83df9301aa5c2a5d284a9940177808c01bc35cef, built on 01/06/2021 20:03 GMT (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,309] INFO Client environment:host.name=localhost (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,309] INFO Client environment:java.version=1.8.0_452 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,309] INFO Client environment:java.vendor=Private Build (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,309] INFO Client environment:java.home=/usr/lib/jvm/java-8-openjdk-amd64/jre (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,309] INFO Client environment:java.class.path=.:/export/server/jdk8/lib/dt.jar:/export/server/jdk8/lib/tools.jar:/export/server/kafka/bin/../libs/activation-1.1.1.jar:/export/server/kafka/bin/../libs/aopalliance-repackaged-2.6.1.jar:/export/server/kafka/bin/../libs/argparse4j-0.7.0.jar:/export/server/kafka/bin/../libs/audience-annotations-0.5.0.jar:/export/server/kafka/bin/../libs/commons-cli-1.4.jar:/export/server/kafka/bin/../libs/commons-lang3-3.8.1.jar:/export/server/kafka/bin/../libs/connect-api-2.8.2.jar:/export/server/kafka/bin/../libs/connect-basic-auth-extension-2.8.2.jar:/export/server/kafka/bin/../libs/connect-file-2.8.2.jar:/export/server/kafka/bin/../libs/connect-json-2.8.2.jar:/export/server/kafka/bin/../libs/connect-mirror-2.8.2.jar:/export/server/kafka/bin/../libs/connect-mirror-client-2.8.2.jar:/export/server/kafka/bin/../libs/connect-runtime-2.8.2.jar:/export/server/kafka/bin/../libs/connect-transforms-2.8.2.jar:/export/server/kafka/bin/../libs/hk2-api-2.6.1.jar:/export/server/kafka/bin/../libs/hk2-locator-2.6.1.jar:/export/server/kafka/bin/../libs/hk2-utils-2.6.1.jar:/export/server/kafka/bin/../libs/jackson-annotations-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-core-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-databind-2.10.5.1.jar:/export/server/kafka/bin/../libs/jackson-dataformat-csv-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-datatype-jdk8-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-jaxrs-base-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-jaxrs-json-provider-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-jaxb-annotations-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-paranamer-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-scala_2.12-2.10.5.jar:/export/server/kafka/bin/../libs/jakarta.activation-api-1.2.1.jar:/export/server/kafka/bin/../libs/jakarta.annotation-api-1.3.5.jar:/export/server/kafka/bin/../libs/jakarta.inject-2.6.1.jar:/export/server/kafka/bin/../libs/jakarta.validation-api-2.0.2.jar:/export/server/kafka/bin/../libs/jakarta.ws.rs-api-2.1.6.jar:/export/server/kafka/bin/../libs/jakarta.xml.bind-api-2.3.2.jar:/export/server/kafka/bin/../libs/javassist-3.27.0-GA.jar:/export/server/kafka/bin/../libs/javax.servlet-api-3.1.0.jar:/export/server/kafka/bin/../libs/javax.ws.rs-api-2.1.1.jar:/export/server/kafka/bin/../libs/jaxb-api-2.3.0.jar:/export/server/kafka/bin/../libs/jersey-client-2.34.jar:/export/server/kafka/bin/../libs/jersey-common-2.34.jar:/export/server/kafka/bin/../libs/jersey-container-servlet-2.34.jar:/export/server/kafka/bin/../libs/jersey-container-servlet-core-2.34.jar:/export/server/kafka/bin/../libs/jersey-hk2-2.34.jar:/export/server/kafka/bin/../libs/jersey-server-2.34.jar:/export/server/kafka/bin/../libs/jetty-client-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-continuation-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-http-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-io-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-security-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-server-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-servlet-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-servlets-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-util-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-util-ajax-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jline-3.12.1.jar:/export/server/kafka/bin/../libs/jopt-simple-5.0.4.jar:/export/server/kafka/bin/../libs/kafka_2.12-2.8.2.jar:/export/server/kafka/bin/../libs/kafka_2.12-2.8.2-sources.jar:/export/server/kafka/bin/../libs/kafka-clients-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-log4j-appender-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-metadata-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-raft-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-shell-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-examples-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-scala_2.12-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-test-utils-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-tools-2.8.2.jar:/export/server/kafka/bin/../libs/log4j-1.2.17.jar:/export/server/kafka/bin/../libs/lz4-java-1.7.1.jar:/export/server/kafka/bin/../libs/maven-artifact-3.8.1.jar:/export/server/kafka/bin/../libs/metrics-core-2.2.0.jar:/export/server/kafka/bin/../libs/netty-buffer-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-codec-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-common-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-handler-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-resolver-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-tcnative-classes-2.0.46.Final.jar:/export/server/kafka/bin/../libs/netty-transport-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-classes-epoll-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-native-epoll-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-native-unix-common-4.1.73.Final.jar:/export/server/kafka/bin/../libs/osgi-resource-locator-1.0.3.jar:/export/server/kafka/bin/../libs/paranamer-2.8.jar:/export/server/kafka/bin/../libs/plexus-utils-3.2.1.jar:/export/server/kafka/bin/../libs/reflections-0.9.12.jar:/export/server/kafka/bin/../libs/rocksdbjni-5.18.4.jar:/export/server/kafka/bin/../libs/scala-collection-compat_2.12-2.3.0.jar:/export/server/kafka/bin/../libs/scala-java8-compat_2.12-0.9.1.jar:/export/server/kafka/bin/../libs/scala-library-2.12.13.jar:/export/server/kafka/bin/../libs/scala-logging_2.12-3.9.2.jar:/export/server/kafka/bin/../libs/scala-reflect-2.12.13.jar:/export/server/kafka/bin/../libs/slf4j-api-1.7.30.jar:/export/server/kafka/bin/../libs/slf4j-log4j12-1.7.30.jar:/export/server/kafka/bin/../libs/snappy-java-1.1.8.1.jar:/export/server/kafka/bin/../libs/zookeeper-3.5.9.jar:/export/server/kafka/bin/../libs/zookeeper-jute-3.5.9.jar:/export/server/kafka/bin/../libs/zstd-jni-1.4.9-1.jar (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:java.library.path=/usr/java/packages/lib/amd64:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:java.io.tmpdir=/tmp (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:java.compiler=<NA> (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.name=Linux (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.arch=amd64 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.version=6.11.0-26-generic (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:user.name=root (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:user.home=/root (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:user.dir=/root/job14 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.memory.free=1010MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.memory.max=1024MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,310] INFO Client environment:os.memory.total=1024MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,315] INFO Initiating client connection, connectString=node1:2181 sessionTimeout=18000 watcher=kafka.zookeeper.ZooKeeperClient$ZooKeeperClientWatcher$@6b2ea799 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 11:01:08,323] INFO jute.maxbuffer value is 4194304 Bytes (org.apache.zookeeper.ClientCnxnSocket)
[2025-06-13 11:01:08,329] INFO zookeeper.request.timeout value is 0. feature enabled= (org.apache.zookeeper.ClientCnxn)
[2025-06-13 11:01:08,331] INFO [ZooKeeperClient Kafka server] Waiting until connected. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 11:01:08,335] INFO Opening socket connection to server node1/127.0.0.1:2181. Will not attempt to authenticate using SASL (unknown error) (org.apache.zookeeper.ClientCnxn)
[2025-06-13 11:01:08,343] INFO Socket connection established, initiating session, client: /127.0.0.1:49520, server: node1/127.0.0.1:2181 (org.apache.zookeeper.ClientCnxn)
[2025-06-13 11:01:08,357] INFO Session establishment complete on server node1/127.0.0.1:2181, sessionid = 0x100004f5f180002, negotiated timeout = 18000 (org.apache.zookeeper.ClientCnxn)
[2025-06-13 11:01:08,364] INFO [ZooKeeperClient Kafka server] Connected. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 11:01:08,535] INFO [feature-zk-node-event-process-thread]: Starting (kafka.server.FinalizedFeatureChangeListener$ChangeNotificationProcessorThread)
[2025-06-13 11:01:08,557] INFO Feature ZK node at path: /feature does not exist (kafka.server.FinalizedFeatureChangeListener)
[2025-06-13 11:01:08,557] INFO Cleared cache (kafka.server.FinalizedFeatureCache)
[2025-06-13 11:01:08,906] INFO Cluster ID = iiC77VKpQGi25v7rIpF1nw (kafka.server.KafkaServer)
[2025-06-13 11:01:08,913] WARN No meta.properties file under dir /export/server/kafka/logs/meta.properties (kafka.server.BrokerMetadataCheckpoint)
[2025-06-13 11:01:09,092] INFO KafkaConfig values: 
	advertised.host.name = null
	advertised.listeners = null
	advertised.port = null
	alter.config.policy.class.name = null
	alter.log.dirs.replication.quota.window.num = 11
	alter.log.dirs.replication.quota.window.size.seconds = 1
	authorizer.class.name = 
	auto.create.topics.enable = true
	auto.leader.rebalance.enable = true
	background.threads = 10
	broker.heartbeat.interval.ms = 2000
	broker.id = 0
	broker.id.generation.enable = true
	broker.rack = null
	broker.session.timeout.ms = 9000
	client.quota.callback.class = null
	compression.type = producer
	connection.failed.authentication.delay.ms = 100
	connections.max.idle.ms = 600000
	connections.max.reauth.ms = 0
	control.plane.listener.name = null
	controlled.shutdown.enable = true
	controlled.shutdown.max.retries = 3
	controlled.shutdown.retry.backoff.ms = 5000
	controller.listener.names = null
	controller.quorum.append.linger.ms = 25
	controller.quorum.election.backoff.max.ms = 1000
	controller.quorum.election.timeout.ms = 1000
	controller.quorum.fetch.timeout.ms = 2000
	controller.quorum.request.timeout.ms = 2000
	controller.quorum.retry.backoff.ms = 20
	controller.quorum.voters = []
	controller.quota.window.num = 11
	controller.quota.window.size.seconds = 1
	controller.socket.timeout.ms = 30000
	create.topic.policy.class.name = null
	default.replication.factor = 1
	delegation.token.expiry.check.interval.ms = 3600000
	delegation.token.expiry.time.ms = 86400000
	delegation.token.master.key = null
	delegation.token.max.lifetime.ms = 604800000
	delegation.token.secret.key = null
	delete.records.purgatory.purge.interval.requests = 1
	delete.topic.enable = true
	fetch.max.bytes = 57671680
	fetch.purgatory.purge.interval.requests = 1000
	group.initial.rebalance.delay.ms = 0
	group.max.session.timeout.ms = 1800000
	group.max.size = 2147483647
	group.min.session.timeout.ms = 6000
	host.name = 
	initial.broker.registration.timeout.ms = 60000
	inter.broker.listener.name = null
	inter.broker.protocol.version = 2.8-IV1
	kafka.metrics.polling.interval.secs = 10
	kafka.metrics.reporters = []
	leader.imbalance.check.interval.seconds = 300
	leader.imbalance.per.broker.percentage = 10
	listener.security.protocol.map = PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
	listeners = PLAINTEXT://node1:9092
	log.cleaner.backoff.ms = 15000
	log.cleaner.dedupe.buffer.size = 134217728
	log.cleaner.delete.retention.ms = 86400000
	log.cleaner.enable = true
	log.cleaner.io.buffer.load.factor = 0.9
	log.cleaner.io.buffer.size = 524288
	log.cleaner.io.max.bytes.per.second = 1.7976931348623157E308
	log.cleaner.max.compaction.lag.ms = 9223372036854775807
	log.cleaner.min.cleanable.ratio = 0.5
	log.cleaner.min.compaction.lag.ms = 0
	log.cleaner.threads = 1
	log.cleanup.policy = [delete]
	log.dir = /tmp/kafka-logs
	log.dirs = /export/server/kafka/logs
	log.flush.interval.messages = 9223372036854775807
	log.flush.interval.ms = null
	log.flush.offset.checkpoint.interval.ms = 60000
	log.flush.scheduler.interval.ms = 9223372036854775807
	log.flush.start.offset.checkpoint.interval.ms = 60000
	log.index.interval.bytes = 4096
	log.index.size.max.bytes = 10485760
	log.message.downconversion.enable = true
	log.message.format.version = 2.8-IV1
	log.message.timestamp.difference.max.ms = 9223372036854775807
	log.message.timestamp.type = CreateTime
	log.preallocate = false
	log.retention.bytes = -1
	log.retention.check.interval.ms = 300000
	log.retention.hours = 168
	log.retention.minutes = null
	log.retention.ms = null
	log.roll.hours = 168
	log.roll.jitter.hours = 0
	log.roll.jitter.ms = null
	log.roll.ms = null
	log.segment.bytes = 1073741824
	log.segment.delete.delay.ms = 60000
	max.connection.creation.rate = 2147483647
	max.connections = 2147483647
	max.connections.per.ip = 2147483647
	max.connections.per.ip.overrides = 
	max.incremental.fetch.session.cache.slots = 1000
	message.max.bytes = 1048588
	metadata.log.dir = null
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	min.insync.replicas = 1
	node.id = -1
	num.io.threads = 8
	num.network.threads = 3
	num.partitions = 1
	num.recovery.threads.per.data.dir = 1
	num.replica.alter.log.dirs.threads = null
	num.replica.fetchers = 1
	offset.metadata.max.bytes = 4096
	offsets.commit.required.acks = -1
	offsets.commit.timeout.ms = 5000
	offsets.load.buffer.size = 5242880
	offsets.retention.check.interval.ms = 600000
	offsets.retention.minutes = 10080
	offsets.topic.compression.codec = 0
	offsets.topic.num.partitions = 50
	offsets.topic.replication.factor = 1
	offsets.topic.segment.bytes = 104857600
	password.encoder.cipher.algorithm = AES/CBC/PKCS5Padding
	password.encoder.iterations = 4096
	password.encoder.key.length = 128
	password.encoder.keyfactory.algorithm = null
	password.encoder.old.secret = null
	password.encoder.secret = null
	port = 9092
	principal.builder.class = null
	process.roles = []
	producer.purgatory.purge.interval.requests = 1000
	queued.max.request.bytes = -1
	queued.max.requests = 500
	quota.consumer.default = 9223372036854775807
	quota.producer.default = 9223372036854775807
	quota.window.num = 11
	quota.window.size.seconds = 1
	replica.fetch.backoff.ms = 1000
	replica.fetch.max.bytes = 1048576
	replica.fetch.min.bytes = 1
	replica.fetch.response.max.bytes = 10485760
	replica.fetch.wait.max.ms = 500
	replica.high.watermark.checkpoint.interval.ms = 5000
	replica.lag.time.max.ms = 30000
	replica.selector.class = null
	replica.socket.receive.buffer.bytes = 65536
	replica.socket.timeout.ms = 30000
	replication.quota.window.num = 11
	replication.quota.window.size.seconds = 1
	request.timeout.ms = 30000
	reserved.broker.max.id = 1000
	sasl.client.callback.handler.class = null
	sasl.enabled.mechanisms = [GSSAPI]
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.principal.to.local.rules = [DEFAULT]
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism.controller.protocol = GSSAPI
	sasl.mechanism.inter.broker.protocol = GSSAPI
	sasl.server.callback.handler.class = null
	sasl.server.max.receive.size = 524288
	security.inter.broker.protocol = PLAINTEXT
	security.providers = null
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	socket.receive.buffer.bytes = 102400
	socket.request.max.bytes = 104857600
	socket.send.buffer.bytes = 102400
	ssl.cipher.suites = []
	ssl.client.auth = none
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.principal.mapping.rules = DEFAULT
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.abort.timed.out.transaction.cleanup.interval.ms = 10000
	transaction.max.timeout.ms = 900000
	transaction.remove.expired.transaction.cleanup.interval.ms = 3600000
	transaction.state.log.load.buffer.size = 5242880
	transaction.state.log.min.isr = 1
	transaction.state.log.num.partitions = 50
	transaction.state.log.replication.factor = 1
	transaction.state.log.segment.bytes = 104857600
	transactional.id.expiration.ms = 604800000
	unclean.leader.election.enable = false
	zookeeper.clientCnxnSocket = null
	zookeeper.connect = node1:2181
	zookeeper.connection.timeout.ms = 18000
	zookeeper.max.in.flight.requests = 10
	zookeeper.session.timeout.ms = 18000
	zookeeper.set.acl = false
	zookeeper.ssl.cipher.suites = null
	zookeeper.ssl.client.enable = false
	zookeeper.ssl.crl.enable = false
	zookeeper.ssl.enabled.protocols = null
	zookeeper.ssl.endpoint.identification.algorithm = HTTPS
	zookeeper.ssl.keystore.location = null
	zookeeper.ssl.keystore.password = null
	zookeeper.ssl.keystore.type = null
	zookeeper.ssl.ocsp.enable = false
	zookeeper.ssl.protocol = TLSv1.2
	zookeeper.ssl.truststore.location = null
	zookeeper.ssl.truststore.password = null
	zookeeper.ssl.truststore.type = null
	zookeeper.sync.time.ms = 2000
 (kafka.server.KafkaConfig)
[2025-06-13 11:01:09,107] INFO KafkaConfig values: 
	advertised.host.name = null
	advertised.listeners = null
	advertised.port = null
	alter.config.policy.class.name = null
	alter.log.dirs.replication.quota.window.num = 11
	alter.log.dirs.replication.quota.window.size.seconds = 1
	authorizer.class.name = 
	auto.create.topics.enable = true
	auto.leader.rebalance.enable = true
	background.threads = 10
	broker.heartbeat.interval.ms = 2000
	broker.id = 0
	broker.id.generation.enable = true
	broker.rack = null
	broker.session.timeout.ms = 9000
	client.quota.callback.class = null
	compression.type = producer
	connection.failed.authentication.delay.ms = 100
	connections.max.idle.ms = 600000
	connections.max.reauth.ms = 0
	control.plane.listener.name = null
	controlled.shutdown.enable = true
	controlled.shutdown.max.retries = 3
	controlled.shutdown.retry.backoff.ms = 5000
	controller.listener.names = null
	controller.quorum.append.linger.ms = 25
	controller.quorum.election.backoff.max.ms = 1000
	controller.quorum.election.timeout.ms = 1000
	controller.quorum.fetch.timeout.ms = 2000
	controller.quorum.request.timeout.ms = 2000
	controller.quorum.retry.backoff.ms = 20
	controller.quorum.voters = []
	controller.quota.window.num = 11
	controller.quota.window.size.seconds = 1
	controller.socket.timeout.ms = 30000
	create.topic.policy.class.name = null
	default.replication.factor = 1
	delegation.token.expiry.check.interval.ms = 3600000
	delegation.token.expiry.time.ms = 86400000
	delegation.token.master.key = null
	delegation.token.max.lifetime.ms = 604800000
	delegation.token.secret.key = null
	delete.records.purgatory.purge.interval.requests = 1
	delete.topic.enable = true
	fetch.max.bytes = 57671680
	fetch.purgatory.purge.interval.requests = 1000
	group.initial.rebalance.delay.ms = 0
	group.max.session.timeout.ms = 1800000
	group.max.size = 2147483647
	group.min.session.timeout.ms = 6000
	host.name = 
	initial.broker.registration.timeout.ms = 60000
	inter.broker.listener.name = null
	inter.broker.protocol.version = 2.8-IV1
	kafka.metrics.polling.interval.secs = 10
	kafka.metrics.reporters = []
	leader.imbalance.check.interval.seconds = 300
	leader.imbalance.per.broker.percentage = 10
	listener.security.protocol.map = PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
	listeners = PLAINTEXT://node1:9092
	log.cleaner.backoff.ms = 15000
	log.cleaner.dedupe.buffer.size = 134217728
	log.cleaner.delete.retention.ms = 86400000
	log.cleaner.enable = true
	log.cleaner.io.buffer.load.factor = 0.9
	log.cleaner.io.buffer.size = 524288
	log.cleaner.io.max.bytes.per.second = 1.7976931348623157E308
	log.cleaner.max.compaction.lag.ms = 9223372036854775807
	log.cleaner.min.cleanable.ratio = 0.5
	log.cleaner.min.compaction.lag.ms = 0
	log.cleaner.threads = 1
	log.cleanup.policy = [delete]
	log.dir = /tmp/kafka-logs
	log.dirs = /export/server/kafka/logs
	log.flush.interval.messages = 9223372036854775807
	log.flush.interval.ms = null
	log.flush.offset.checkpoint.interval.ms = 60000
	log.flush.scheduler.interval.ms = 9223372036854775807
	log.flush.start.offset.checkpoint.interval.ms = 60000
	log.index.interval.bytes = 4096
	log.index.size.max.bytes = 10485760
	log.message.downconversion.enable = true
	log.message.format.version = 2.8-IV1
	log.message.timestamp.difference.max.ms = 9223372036854775807
	log.message.timestamp.type = CreateTime
	log.preallocate = false
	log.retention.bytes = -1
	log.retention.check.interval.ms = 300000
	log.retention.hours = 168
	log.retention.minutes = null
	log.retention.ms = null
	log.roll.hours = 168
	log.roll.jitter.hours = 0
	log.roll.jitter.ms = null
	log.roll.ms = null
	log.segment.bytes = 1073741824
	log.segment.delete.delay.ms = 60000
	max.connection.creation.rate = 2147483647
	max.connections = 2147483647
	max.connections.per.ip = 2147483647
	max.connections.per.ip.overrides = 
	max.incremental.fetch.session.cache.slots = 1000
	message.max.bytes = 1048588
	metadata.log.dir = null
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	min.insync.replicas = 1
	node.id = -1
	num.io.threads = 8
	num.network.threads = 3
	num.partitions = 1
	num.recovery.threads.per.data.dir = 1
	num.replica.alter.log.dirs.threads = null
	num.replica.fetchers = 1
	offset.metadata.max.bytes = 4096
	offsets.commit.required.acks = -1
	offsets.commit.timeout.ms = 5000
	offsets.load.buffer.size = 5242880
	offsets.retention.check.interval.ms = 600000
	offsets.retention.minutes = 10080
	offsets.topic.compression.codec = 0
	offsets.topic.num.partitions = 50
	offsets.topic.replication.factor = 1
	offsets.topic.segment.bytes = 104857600
	password.encoder.cipher.algorithm = AES/CBC/PKCS5Padding
	password.encoder.iterations = 4096
	password.encoder.key.length = 128
	password.encoder.keyfactory.algorithm = null
	password.encoder.old.secret = null
	password.encoder.secret = null
	port = 9092
	principal.builder.class = null
	process.roles = []
	producer.purgatory.purge.interval.requests = 1000
	queued.max.request.bytes = -1
	queued.max.requests = 500
	quota.consumer.default = 9223372036854775807
	quota.producer.default = 9223372036854775807
	quota.window.num = 11
	quota.window.size.seconds = 1
	replica.fetch.backoff.ms = 1000
	replica.fetch.max.bytes = 1048576
	replica.fetch.min.bytes = 1
	replica.fetch.response.max.bytes = 10485760
	replica.fetch.wait.max.ms = 500
	replica.high.watermark.checkpoint.interval.ms = 5000
	replica.lag.time.max.ms = 30000
	replica.selector.class = null
	replica.socket.receive.buffer.bytes = 65536
	replica.socket.timeout.ms = 30000
	replication.quota.window.num = 11
	replication.quota.window.size.seconds = 1
	request.timeout.ms = 30000
	reserved.broker.max.id = 1000
	sasl.client.callback.handler.class = null
	sasl.enabled.mechanisms = [GSSAPI]
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.principal.to.local.rules = [DEFAULT]
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.mechanism.controller.protocol = GSSAPI
	sasl.mechanism.inter.broker.protocol = GSSAPI
	sasl.server.callback.handler.class = null
	sasl.server.max.receive.size = 524288
	security.inter.broker.protocol = PLAINTEXT
	security.providers = null
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	socket.receive.buffer.bytes = 102400
	socket.request.max.bytes = 104857600
	socket.send.buffer.bytes = 102400
	ssl.cipher.suites = []
	ssl.client.auth = none
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.principal.mapping.rules = DEFAULT
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.abort.timed.out.transaction.cleanup.interval.ms = 10000
	transaction.max.timeout.ms = 900000
	transaction.remove.expired.transaction.cleanup.interval.ms = 3600000
	transaction.state.log.load.buffer.size = 5242880
	transaction.state.log.min.isr = 1
	transaction.state.log.num.partitions = 50
	transaction.state.log.replication.factor = 1
	transaction.state.log.segment.bytes = 104857600
	transactional.id.expiration.ms = 604800000
	unclean.leader.election.enable = false
	zookeeper.clientCnxnSocket = null
	zookeeper.connect = node1:2181
	zookeeper.connection.timeout.ms = 18000
	zookeeper.max.in.flight.requests = 10
	zookeeper.session.timeout.ms = 18000
	zookeeper.set.acl = false
	zookeeper.ssl.cipher.suites = null
	zookeeper.ssl.client.enable = false
	zookeeper.ssl.crl.enable = false
	zookeeper.ssl.enabled.protocols = null
	zookeeper.ssl.endpoint.identification.algorithm = HTTPS
	zookeeper.ssl.keystore.location = null
	zookeeper.ssl.keystore.password = null
	zookeeper.ssl.keystore.type = null
	zookeeper.ssl.ocsp.enable = false
	zookeeper.ssl.protocol = TLSv1.2
	zookeeper.ssl.truststore.location = null
	zookeeper.ssl.truststore.password = null
	zookeeper.ssl.truststore.type = null
	zookeeper.sync.time.ms = 2000
 (kafka.server.KafkaConfig)
[2025-06-13 11:01:09,168] INFO [ThrottledChannelReaper-Fetch]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-06-13 11:01:09,169] INFO [ThrottledChannelReaper-Produce]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-06-13 11:01:09,170] INFO [ThrottledChannelReaper-Request]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-06-13 11:01:09,171] INFO [ThrottledChannelReaper-ControllerMutation]: Starting (kafka.server.ClientQuotaManager$ThrottledChannelReaper)
[2025-06-13 11:01:09,225] INFO Loading logs from log dirs ArrayBuffer(/export/server/kafka/logs) (kafka.log.LogManager)
[2025-06-13 11:01:09,231] INFO Skipping recovery for all logs in /export/server/kafka/logs since clean shutdown file was found (kafka.log.LogManager)
[2025-06-13 11:01:09,242] INFO Loaded 0 logs in 17ms. (kafka.log.LogManager)
[2025-06-13 11:01:09,242] INFO Starting log cleanup with a period of 300000 ms. (kafka.log.LogManager)
[2025-06-13 11:01:09,245] INFO Starting log flusher with a default period of 9223372036854775807 ms. (kafka.log.LogManager)
[2025-06-13 11:01:10,296] INFO Updated connection-accept-rate max connection creation rate to 2147483647 (kafka.network.ConnectionQuotas)
[2025-06-13 11:01:10,300] INFO Awaiting socket connections on node1:9092. (kafka.network.Acceptor)
[2025-06-13 11:01:10,339] INFO [SocketServer listenerType=ZK_BROKER, nodeId=0] Created data-plane acceptor and processors for endpoint : ListenerName(PLAINTEXT) (kafka.network.SocketServer)
[2025-06-13 11:01:10,394] INFO [broker-0-to-controller-send-thread]: Starting (kafka.server.BrokerToControllerRequestThread)
[2025-06-13 11:01:10,435] INFO [ExpirationReaper-0-Produce]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,436] INFO [ExpirationReaper-0-Fetch]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,437] INFO [ExpirationReaper-0-DeleteRecords]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,437] INFO [ExpirationReaper-0-ElectLeader]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,454] INFO [LogDirFailureHandler]: Starting (kafka.server.ReplicaManager$LogDirFailureHandler)
[2025-06-13 11:01:10,550] INFO Creating /brokers/ids/0 (is it secure? false) (kafka.zk.KafkaZkClient)
[2025-06-13 11:01:10,580] INFO Stat of the created znode at /brokers/ids/0 is: 57,57,1749783670568,1749783670568,1,0,0,72057934935752706,194,0,57
 (kafka.zk.KafkaZkClient)
[2025-06-13 11:01:10,581] INFO Registered broker 0 at path /brokers/ids/0 with addresses: PLAINTEXT://node1:9092, czxid (broker epoch): 57 (kafka.zk.KafkaZkClient)
[2025-06-13 11:01:10,706] INFO [ExpirationReaper-0-topic]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,715] INFO [ExpirationReaper-0-Heartbeat]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,715] INFO [ExpirationReaper-0-Rebalance]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,732] INFO Successfully created /controller_epoch with initial epoch 0 (kafka.zk.KafkaZkClient)
[2025-06-13 11:01:10,748] INFO [GroupCoordinator 0]: Starting up. (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:01:10,761] INFO [GroupCoordinator 0]: Startup complete. (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:01:10,762] INFO Feature ZK node created at path: /feature (kafka.server.FinalizedFeatureChangeListener)
[2025-06-13 11:01:10,856] INFO Updated cache from existing <empty> to latest FinalizedFeaturesAndEpoch(features=Features{}, epoch=0). (kafka.server.FinalizedFeatureCache)
[2025-06-13 11:01:10,870] INFO [ProducerId Manager 0]: Acquired new producerId block (brokerId:0,blockStartProducerId:0,blockEndProducerId:999) by writing to Zk with path version 1 (kafka.coordinator.transaction.ProducerIdManager)
[2025-06-13 11:01:10,872] INFO [TransactionCoordinator id=0] Starting up. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-06-13 11:01:10,878] INFO [Transaction Marker Channel Manager 0]: Starting (kafka.coordinator.transaction.TransactionMarkerChannelManager)
[2025-06-13 11:01:10,879] INFO [TransactionCoordinator id=0] Startup complete. (kafka.coordinator.transaction.TransactionCoordinator)
[2025-06-13 11:01:10,915] INFO [ExpirationReaper-0-AlterAcls]: Starting (kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper)
[2025-06-13 11:01:10,952] INFO [/config/changes-event-process-thread]: Starting (kafka.common.ZkNodeChangeNotificationListener$ChangeEventProcessThread)
[2025-06-13 11:01:10,983] INFO [SocketServer listenerType=ZK_BROKER, nodeId=0] Starting socket server acceptors and processors (kafka.network.SocketServer)
[2025-06-13 11:01:10,989] INFO [SocketServer listenerType=ZK_BROKER, nodeId=0] Started data-plane acceptor and processor(s) for endpoint : ListenerName(PLAINTEXT) (kafka.network.SocketServer)
[2025-06-13 11:01:10,990] INFO [SocketServer listenerType=ZK_BROKER, nodeId=0] Started socket server acceptors and processors (kafka.network.SocketServer)
[2025-06-13 11:01:11,000] INFO Kafka version: 2.8.2 (org.apache.kafka.common.utils.AppInfoParser)
[2025-06-13 11:01:11,000] INFO Kafka commitId: 3146c6ff4a24cc24 (org.apache.kafka.common.utils.AppInfoParser)
[2025-06-13 11:01:11,000] INFO Kafka startTimeMs: 1749783670990 (org.apache.kafka.common.utils.AppInfoParser)
[2025-06-13 11:01:11,006] INFO [KafkaServer id=0] started (kafka.server.KafkaServer)
[2025-06-13 11:01:11,150] INFO [broker-0-to-controller-send-thread]: Recorded new controller, from now on will use broker node1:9092 (id: 0 rack: null) (kafka.server.BrokerToControllerRequestThread)
[2025-06-13 11:02:21,662] INFO Creating topic attendance with configuration {} and initial partition assignment Map(2 -> ArrayBuffer(0), 1 -> ArrayBuffer(0), 0 -> ArrayBuffer(0)) (kafka.zk.AdminZkClient)
[2025-06-13 11:02:21,842] INFO [ReplicaFetcherManager on broker 0] Removed fetcher for partitions Set(attendance-0, attendance-1, attendance-2) (kafka.server.ReplicaFetcherManager)
[2025-06-13 11:02:21,946] INFO [Log partition=attendance-0, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:21,951] INFO Created log for partition attendance-0 in /export/server/kafka/logs/attendance-0 with properties {} (kafka.log.LogManager)
[2025-06-13 11:02:21,953] INFO [Partition attendance-0 broker=0] No checkpointed highwatermark is found for partition attendance-0 (kafka.cluster.Partition)
[2025-06-13 11:02:21,955] INFO [Partition attendance-0 broker=0] Log loaded for partition attendance-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:21,991] INFO [Log partition=attendance-1, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:21,992] INFO Created log for partition attendance-1 in /export/server/kafka/logs/attendance-1 with properties {} (kafka.log.LogManager)
[2025-06-13 11:02:21,993] INFO [Partition attendance-1 broker=0] No checkpointed highwatermark is found for partition attendance-1 (kafka.cluster.Partition)
[2025-06-13 11:02:21,993] INFO [Partition attendance-1 broker=0] Log loaded for partition attendance-1 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:22,008] INFO [Log partition=attendance-2, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:22,010] INFO Created log for partition attendance-2 in /export/server/kafka/logs/attendance-2 with properties {} (kafka.log.LogManager)
[2025-06-13 11:02:22,010] INFO [Partition attendance-2 broker=0] No checkpointed highwatermark is found for partition attendance-2 (kafka.cluster.Partition)
[2025-06-13 11:02:22,010] INFO [Partition attendance-2 broker=0] Log loaded for partition attendance-2 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:41,865] INFO Creating topic __consumer_offsets with configuration {segment.bytes=104857600, cleanup.policy=compact, compression.type=producer} and initial partition assignment Map(23 -> ArrayBuffer(0), 32 -> ArrayBuffer(0), 41 -> ArrayBuffer(0), 17 -> ArrayBuffer(0), 8 -> ArrayBuffer(0), 35 -> ArrayBuffer(0), 44 -> ArrayBuffer(0), 26 -> ArrayBuffer(0), 11 -> ArrayBuffer(0), 29 -> ArrayBuffer(0), 38 -> ArrayBuffer(0), 47 -> ArrayBuffer(0), 20 -> ArrayBuffer(0), 2 -> ArrayBuffer(0), 5 -> ArrayBuffer(0), 14 -> ArrayBuffer(0), 46 -> ArrayBuffer(0), 49 -> ArrayBuffer(0), 40 -> ArrayBuffer(0), 13 -> ArrayBuffer(0), 4 -> ArrayBuffer(0), 22 -> ArrayBuffer(0), 31 -> ArrayBuffer(0), 16 -> ArrayBuffer(0), 7 -> ArrayBuffer(0), 43 -> ArrayBuffer(0), 25 -> ArrayBuffer(0), 34 -> ArrayBuffer(0), 10 -> ArrayBuffer(0), 37 -> ArrayBuffer(0), 1 -> ArrayBuffer(0), 19 -> ArrayBuffer(0), 28 -> ArrayBuffer(0), 45 -> ArrayBuffer(0), 27 -> ArrayBuffer(0), 36 -> ArrayBuffer(0), 18 -> ArrayBuffer(0), 9 -> ArrayBuffer(0), 21 -> ArrayBuffer(0), 48 -> ArrayBuffer(0), 3 -> ArrayBuffer(0), 12 -> ArrayBuffer(0), 30 -> ArrayBuffer(0), 39 -> ArrayBuffer(0), 15 -> ArrayBuffer(0), 42 -> ArrayBuffer(0), 24 -> ArrayBuffer(0), 6 -> ArrayBuffer(0), 33 -> ArrayBuffer(0), 0 -> ArrayBuffer(0)) (kafka.zk.AdminZkClient)
[2025-06-13 11:02:42,214] INFO [ReplicaFetcherManager on broker 0] Removed fetcher for partitions Set(__consumer_offsets-22, __consumer_offsets-30, __consumer_offsets-8, __consumer_offsets-21, __consumer_offsets-4, __consumer_offsets-27, __consumer_offsets-7, __consumer_offsets-9, __consumer_offsets-46, __consumer_offsets-25, __consumer_offsets-35, __consumer_offsets-41, __consumer_offsets-33, __consumer_offsets-23, __consumer_offsets-49, __consumer_offsets-47, __consumer_offsets-16, __consumer_offsets-28, __consumer_offsets-31, __consumer_offsets-36, __consumer_offsets-42, __consumer_offsets-3, __consumer_offsets-18, __consumer_offsets-37, __consumer_offsets-15, __consumer_offsets-24, __consumer_offsets-38, __consumer_offsets-17, __consumer_offsets-48, __consumer_offsets-19, __consumer_offsets-11, __consumer_offsets-13, __consumer_offsets-2, __consumer_offsets-43, __consumer_offsets-6, __consumer_offsets-14, __consumer_offsets-20, __consumer_offsets-0, __consumer_offsets-44, __consumer_offsets-39, __consumer_offsets-12, __consumer_offsets-45, __consumer_offsets-1, __consumer_offsets-5, __consumer_offsets-26, __consumer_offsets-29, __consumer_offsets-34, __consumer_offsets-10, __consumer_offsets-32, __consumer_offsets-40) (kafka.server.ReplicaFetcherManager)
[2025-06-13 11:02:42,224] INFO [Log partition=__consumer_offsets-0, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,226] INFO Created log for partition __consumer_offsets-0 in /export/server/kafka/logs/__consumer_offsets-0 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,227] INFO [Partition __consumer_offsets-0 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,228] INFO [Partition __consumer_offsets-0 broker=0] Log loaded for partition __consumer_offsets-0 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,240] INFO [Log partition=__consumer_offsets-29, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,242] INFO Created log for partition __consumer_offsets-29 in /export/server/kafka/logs/__consumer_offsets-29 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,242] INFO [Partition __consumer_offsets-29 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-29 (kafka.cluster.Partition)
[2025-06-13 11:02:42,242] INFO [Partition __consumer_offsets-29 broker=0] Log loaded for partition __consumer_offsets-29 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,249] INFO [Log partition=__consumer_offsets-48, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,252] INFO Created log for partition __consumer_offsets-48 in /export/server/kafka/logs/__consumer_offsets-48 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,252] INFO [Partition __consumer_offsets-48 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-48 (kafka.cluster.Partition)
[2025-06-13 11:02:42,252] INFO [Partition __consumer_offsets-48 broker=0] Log loaded for partition __consumer_offsets-48 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,263] INFO [Log partition=__consumer_offsets-10, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,265] INFO Created log for partition __consumer_offsets-10 in /export/server/kafka/logs/__consumer_offsets-10 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,265] INFO [Partition __consumer_offsets-10 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-10 (kafka.cluster.Partition)
[2025-06-13 11:02:42,266] INFO [Partition __consumer_offsets-10 broker=0] Log loaded for partition __consumer_offsets-10 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,275] INFO [Log partition=__consumer_offsets-45, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,276] INFO Created log for partition __consumer_offsets-45 in /export/server/kafka/logs/__consumer_offsets-45 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,276] INFO [Partition __consumer_offsets-45 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-45 (kafka.cluster.Partition)
[2025-06-13 11:02:42,276] INFO [Partition __consumer_offsets-45 broker=0] Log loaded for partition __consumer_offsets-45 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,303] INFO [Log partition=__consumer_offsets-26, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,304] INFO Created log for partition __consumer_offsets-26 in /export/server/kafka/logs/__consumer_offsets-26 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,304] INFO [Partition __consumer_offsets-26 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-26 (kafka.cluster.Partition)
[2025-06-13 11:02:42,304] INFO [Partition __consumer_offsets-26 broker=0] Log loaded for partition __consumer_offsets-26 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,322] INFO [Log partition=__consumer_offsets-7, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,323] INFO Created log for partition __consumer_offsets-7 in /export/server/kafka/logs/__consumer_offsets-7 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,323] INFO [Partition __consumer_offsets-7 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-7 (kafka.cluster.Partition)
[2025-06-13 11:02:42,323] INFO [Partition __consumer_offsets-7 broker=0] Log loaded for partition __consumer_offsets-7 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,330] INFO [Log partition=__consumer_offsets-42, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,331] INFO Created log for partition __consumer_offsets-42 in /export/server/kafka/logs/__consumer_offsets-42 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,331] INFO [Partition __consumer_offsets-42 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-42 (kafka.cluster.Partition)
[2025-06-13 11:02:42,331] INFO [Partition __consumer_offsets-42 broker=0] Log loaded for partition __consumer_offsets-42 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,339] INFO [Log partition=__consumer_offsets-4, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,340] INFO Created log for partition __consumer_offsets-4 in /export/server/kafka/logs/__consumer_offsets-4 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,340] INFO [Partition __consumer_offsets-4 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-4 (kafka.cluster.Partition)
[2025-06-13 11:02:42,340] INFO [Partition __consumer_offsets-4 broker=0] Log loaded for partition __consumer_offsets-4 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,351] INFO [Log partition=__consumer_offsets-23, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,352] INFO Created log for partition __consumer_offsets-23 in /export/server/kafka/logs/__consumer_offsets-23 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,352] INFO [Partition __consumer_offsets-23 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-23 (kafka.cluster.Partition)
[2025-06-13 11:02:42,352] INFO [Partition __consumer_offsets-23 broker=0] Log loaded for partition __consumer_offsets-23 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,358] INFO [Log partition=__consumer_offsets-1, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,359] INFO Created log for partition __consumer_offsets-1 in /export/server/kafka/logs/__consumer_offsets-1 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,359] INFO [Partition __consumer_offsets-1 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-1 (kafka.cluster.Partition)
[2025-06-13 11:02:42,359] INFO [Partition __consumer_offsets-1 broker=0] Log loaded for partition __consumer_offsets-1 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,366] INFO [Log partition=__consumer_offsets-20, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,366] INFO Created log for partition __consumer_offsets-20 in /export/server/kafka/logs/__consumer_offsets-20 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,366] INFO [Partition __consumer_offsets-20 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-20 (kafka.cluster.Partition)
[2025-06-13 11:02:42,366] INFO [Partition __consumer_offsets-20 broker=0] Log loaded for partition __consumer_offsets-20 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,375] INFO [Log partition=__consumer_offsets-39, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,377] INFO Created log for partition __consumer_offsets-39 in /export/server/kafka/logs/__consumer_offsets-39 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,377] INFO [Partition __consumer_offsets-39 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-39 (kafka.cluster.Partition)
[2025-06-13 11:02:42,377] INFO [Partition __consumer_offsets-39 broker=0] Log loaded for partition __consumer_offsets-39 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,387] INFO [Log partition=__consumer_offsets-17, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,389] INFO Created log for partition __consumer_offsets-17 in /export/server/kafka/logs/__consumer_offsets-17 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,389] INFO [Partition __consumer_offsets-17 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-17 (kafka.cluster.Partition)
[2025-06-13 11:02:42,389] INFO [Partition __consumer_offsets-17 broker=0] Log loaded for partition __consumer_offsets-17 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,401] INFO [Log partition=__consumer_offsets-36, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,402] INFO Created log for partition __consumer_offsets-36 in /export/server/kafka/logs/__consumer_offsets-36 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,402] INFO [Partition __consumer_offsets-36 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-36 (kafka.cluster.Partition)
[2025-06-13 11:02:42,402] INFO [Partition __consumer_offsets-36 broker=0] Log loaded for partition __consumer_offsets-36 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,408] INFO [Log partition=__consumer_offsets-14, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,409] INFO Created log for partition __consumer_offsets-14 in /export/server/kafka/logs/__consumer_offsets-14 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,409] INFO [Partition __consumer_offsets-14 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-14 (kafka.cluster.Partition)
[2025-06-13 11:02:42,409] INFO [Partition __consumer_offsets-14 broker=0] Log loaded for partition __consumer_offsets-14 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,419] INFO [Log partition=__consumer_offsets-33, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,421] INFO Created log for partition __consumer_offsets-33 in /export/server/kafka/logs/__consumer_offsets-33 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,421] INFO [Partition __consumer_offsets-33 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-33 (kafka.cluster.Partition)
[2025-06-13 11:02:42,421] INFO [Partition __consumer_offsets-33 broker=0] Log loaded for partition __consumer_offsets-33 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,432] INFO [Log partition=__consumer_offsets-49, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,432] INFO Created log for partition __consumer_offsets-49 in /export/server/kafka/logs/__consumer_offsets-49 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,432] INFO [Partition __consumer_offsets-49 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-49 (kafka.cluster.Partition)
[2025-06-13 11:02:42,432] INFO [Partition __consumer_offsets-49 broker=0] Log loaded for partition __consumer_offsets-49 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,441] INFO [Log partition=__consumer_offsets-11, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,443] INFO Created log for partition __consumer_offsets-11 in /export/server/kafka/logs/__consumer_offsets-11 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,443] INFO [Partition __consumer_offsets-11 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-11 (kafka.cluster.Partition)
[2025-06-13 11:02:42,443] INFO [Partition __consumer_offsets-11 broker=0] Log loaded for partition __consumer_offsets-11 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,454] INFO [Log partition=__consumer_offsets-30, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,454] INFO Created log for partition __consumer_offsets-30 in /export/server/kafka/logs/__consumer_offsets-30 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,455] INFO [Partition __consumer_offsets-30 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-30 (kafka.cluster.Partition)
[2025-06-13 11:02:42,455] INFO [Partition __consumer_offsets-30 broker=0] Log loaded for partition __consumer_offsets-30 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,465] INFO [Log partition=__consumer_offsets-46, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,466] INFO Created log for partition __consumer_offsets-46 in /export/server/kafka/logs/__consumer_offsets-46 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,466] INFO [Partition __consumer_offsets-46 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-46 (kafka.cluster.Partition)
[2025-06-13 11:02:42,466] INFO [Partition __consumer_offsets-46 broker=0] Log loaded for partition __consumer_offsets-46 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,477] INFO [Log partition=__consumer_offsets-27, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,479] INFO Created log for partition __consumer_offsets-27 in /export/server/kafka/logs/__consumer_offsets-27 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,479] INFO [Partition __consumer_offsets-27 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-27 (kafka.cluster.Partition)
[2025-06-13 11:02:42,479] INFO [Partition __consumer_offsets-27 broker=0] Log loaded for partition __consumer_offsets-27 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,489] INFO [Log partition=__consumer_offsets-8, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,490] INFO Created log for partition __consumer_offsets-8 in /export/server/kafka/logs/__consumer_offsets-8 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,490] INFO [Partition __consumer_offsets-8 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-8 (kafka.cluster.Partition)
[2025-06-13 11:02:42,491] INFO [Partition __consumer_offsets-8 broker=0] Log loaded for partition __consumer_offsets-8 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,502] INFO [Log partition=__consumer_offsets-24, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,504] INFO Created log for partition __consumer_offsets-24 in /export/server/kafka/logs/__consumer_offsets-24 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,504] INFO [Partition __consumer_offsets-24 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-24 (kafka.cluster.Partition)
[2025-06-13 11:02:42,505] INFO [Partition __consumer_offsets-24 broker=0] Log loaded for partition __consumer_offsets-24 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,514] INFO [Log partition=__consumer_offsets-43, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,515] INFO Created log for partition __consumer_offsets-43 in /export/server/kafka/logs/__consumer_offsets-43 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,515] INFO [Partition __consumer_offsets-43 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-43 (kafka.cluster.Partition)
[2025-06-13 11:02:42,515] INFO [Partition __consumer_offsets-43 broker=0] Log loaded for partition __consumer_offsets-43 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,526] INFO [Log partition=__consumer_offsets-5, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,527] INFO Created log for partition __consumer_offsets-5 in /export/server/kafka/logs/__consumer_offsets-5 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,527] INFO [Partition __consumer_offsets-5 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-5 (kafka.cluster.Partition)
[2025-06-13 11:02:42,527] INFO [Partition __consumer_offsets-5 broker=0] Log loaded for partition __consumer_offsets-5 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,538] INFO [Log partition=__consumer_offsets-21, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,540] INFO Created log for partition __consumer_offsets-21 in /export/server/kafka/logs/__consumer_offsets-21 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,540] INFO [Partition __consumer_offsets-21 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-21 (kafka.cluster.Partition)
[2025-06-13 11:02:42,540] INFO [Partition __consumer_offsets-21 broker=0] Log loaded for partition __consumer_offsets-21 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,549] INFO [Log partition=__consumer_offsets-40, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,551] INFO Created log for partition __consumer_offsets-40 in /export/server/kafka/logs/__consumer_offsets-40 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,551] INFO [Partition __consumer_offsets-40 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-40 (kafka.cluster.Partition)
[2025-06-13 11:02:42,551] INFO [Partition __consumer_offsets-40 broker=0] Log loaded for partition __consumer_offsets-40 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,561] INFO [Log partition=__consumer_offsets-2, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,561] INFO Created log for partition __consumer_offsets-2 in /export/server/kafka/logs/__consumer_offsets-2 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,561] INFO [Partition __consumer_offsets-2 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-2 (kafka.cluster.Partition)
[2025-06-13 11:02:42,562] INFO [Partition __consumer_offsets-2 broker=0] Log loaded for partition __consumer_offsets-2 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,568] INFO [Log partition=__consumer_offsets-37, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,569] INFO Created log for partition __consumer_offsets-37 in /export/server/kafka/logs/__consumer_offsets-37 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,569] INFO [Partition __consumer_offsets-37 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-37 (kafka.cluster.Partition)
[2025-06-13 11:02:42,569] INFO [Partition __consumer_offsets-37 broker=0] Log loaded for partition __consumer_offsets-37 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,578] INFO [Log partition=__consumer_offsets-18, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,578] INFO Created log for partition __consumer_offsets-18 in /export/server/kafka/logs/__consumer_offsets-18 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,578] INFO [Partition __consumer_offsets-18 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-18 (kafka.cluster.Partition)
[2025-06-13 11:02:42,578] INFO [Partition __consumer_offsets-18 broker=0] Log loaded for partition __consumer_offsets-18 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,589] INFO [Log partition=__consumer_offsets-34, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,590] INFO Created log for partition __consumer_offsets-34 in /export/server/kafka/logs/__consumer_offsets-34 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,590] INFO [Partition __consumer_offsets-34 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-34 (kafka.cluster.Partition)
[2025-06-13 11:02:42,590] INFO [Partition __consumer_offsets-34 broker=0] Log loaded for partition __consumer_offsets-34 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,600] INFO [Log partition=__consumer_offsets-15, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,602] INFO Created log for partition __consumer_offsets-15 in /export/server/kafka/logs/__consumer_offsets-15 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,602] INFO [Partition __consumer_offsets-15 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-15 (kafka.cluster.Partition)
[2025-06-13 11:02:42,602] INFO [Partition __consumer_offsets-15 broker=0] Log loaded for partition __consumer_offsets-15 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,612] INFO [Log partition=__consumer_offsets-12, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,614] INFO Created log for partition __consumer_offsets-12 in /export/server/kafka/logs/__consumer_offsets-12 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,614] INFO [Partition __consumer_offsets-12 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-12 (kafka.cluster.Partition)
[2025-06-13 11:02:42,614] INFO [Partition __consumer_offsets-12 broker=0] Log loaded for partition __consumer_offsets-12 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,622] INFO [Log partition=__consumer_offsets-31, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,622] INFO Created log for partition __consumer_offsets-31 in /export/server/kafka/logs/__consumer_offsets-31 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,622] INFO [Partition __consumer_offsets-31 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-31 (kafka.cluster.Partition)
[2025-06-13 11:02:42,622] INFO [Partition __consumer_offsets-31 broker=0] Log loaded for partition __consumer_offsets-31 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,635] INFO [Log partition=__consumer_offsets-9, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,636] INFO Created log for partition __consumer_offsets-9 in /export/server/kafka/logs/__consumer_offsets-9 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,637] INFO [Partition __consumer_offsets-9 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-9 (kafka.cluster.Partition)
[2025-06-13 11:02:42,637] INFO [Partition __consumer_offsets-9 broker=0] Log loaded for partition __consumer_offsets-9 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,647] INFO [Log partition=__consumer_offsets-47, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,649] INFO Created log for partition __consumer_offsets-47 in /export/server/kafka/logs/__consumer_offsets-47 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,649] INFO [Partition __consumer_offsets-47 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-47 (kafka.cluster.Partition)
[2025-06-13 11:02:42,649] INFO [Partition __consumer_offsets-47 broker=0] Log loaded for partition __consumer_offsets-47 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,656] INFO [Log partition=__consumer_offsets-19, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,657] INFO Created log for partition __consumer_offsets-19 in /export/server/kafka/logs/__consumer_offsets-19 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,657] INFO [Partition __consumer_offsets-19 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-19 (kafka.cluster.Partition)
[2025-06-13 11:02:42,657] INFO [Partition __consumer_offsets-19 broker=0] Log loaded for partition __consumer_offsets-19 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,666] INFO [Log partition=__consumer_offsets-28, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,667] INFO Created log for partition __consumer_offsets-28 in /export/server/kafka/logs/__consumer_offsets-28 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,667] INFO [Partition __consumer_offsets-28 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-28 (kafka.cluster.Partition)
[2025-06-13 11:02:42,667] INFO [Partition __consumer_offsets-28 broker=0] Log loaded for partition __consumer_offsets-28 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,676] INFO [Log partition=__consumer_offsets-38, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,677] INFO Created log for partition __consumer_offsets-38 in /export/server/kafka/logs/__consumer_offsets-38 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,678] INFO [Partition __consumer_offsets-38 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-38 (kafka.cluster.Partition)
[2025-06-13 11:02:42,678] INFO [Partition __consumer_offsets-38 broker=0] Log loaded for partition __consumer_offsets-38 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,686] INFO [Log partition=__consumer_offsets-35, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,686] INFO Created log for partition __consumer_offsets-35 in /export/server/kafka/logs/__consumer_offsets-35 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,686] INFO [Partition __consumer_offsets-35 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-35 (kafka.cluster.Partition)
[2025-06-13 11:02:42,687] INFO [Partition __consumer_offsets-35 broker=0] Log loaded for partition __consumer_offsets-35 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,692] INFO [Log partition=__consumer_offsets-6, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,693] INFO Created log for partition __consumer_offsets-6 in /export/server/kafka/logs/__consumer_offsets-6 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,693] INFO [Partition __consumer_offsets-6 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-6 (kafka.cluster.Partition)
[2025-06-13 11:02:42,693] INFO [Partition __consumer_offsets-6 broker=0] Log loaded for partition __consumer_offsets-6 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,702] INFO [Log partition=__consumer_offsets-44, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,704] INFO Created log for partition __consumer_offsets-44 in /export/server/kafka/logs/__consumer_offsets-44 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,704] INFO [Partition __consumer_offsets-44 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-44 (kafka.cluster.Partition)
[2025-06-13 11:02:42,704] INFO [Partition __consumer_offsets-44 broker=0] Log loaded for partition __consumer_offsets-44 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,712] INFO [Log partition=__consumer_offsets-25, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,714] INFO Created log for partition __consumer_offsets-25 in /export/server/kafka/logs/__consumer_offsets-25 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,714] INFO [Partition __consumer_offsets-25 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-25 (kafka.cluster.Partition)
[2025-06-13 11:02:42,714] INFO [Partition __consumer_offsets-25 broker=0] Log loaded for partition __consumer_offsets-25 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,722] INFO [Log partition=__consumer_offsets-16, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,723] INFO Created log for partition __consumer_offsets-16 in /export/server/kafka/logs/__consumer_offsets-16 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,723] INFO [Partition __consumer_offsets-16 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-16 (kafka.cluster.Partition)
[2025-06-13 11:02:42,723] INFO [Partition __consumer_offsets-16 broker=0] Log loaded for partition __consumer_offsets-16 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,730] INFO [Log partition=__consumer_offsets-22, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,731] INFO Created log for partition __consumer_offsets-22 in /export/server/kafka/logs/__consumer_offsets-22 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,731] INFO [Partition __consumer_offsets-22 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-22 (kafka.cluster.Partition)
[2025-06-13 11:02:42,731] INFO [Partition __consumer_offsets-22 broker=0] Log loaded for partition __consumer_offsets-22 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,741] INFO [Log partition=__consumer_offsets-41, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,743] INFO Created log for partition __consumer_offsets-41 in /export/server/kafka/logs/__consumer_offsets-41 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,743] INFO [Partition __consumer_offsets-41 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-41 (kafka.cluster.Partition)
[2025-06-13 11:02:42,743] INFO [Partition __consumer_offsets-41 broker=0] Log loaded for partition __consumer_offsets-41 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,753] INFO [Log partition=__consumer_offsets-32, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,754] INFO Created log for partition __consumer_offsets-32 in /export/server/kafka/logs/__consumer_offsets-32 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,754] INFO [Partition __consumer_offsets-32 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-32 (kafka.cluster.Partition)
[2025-06-13 11:02:42,755] INFO [Partition __consumer_offsets-32 broker=0] Log loaded for partition __consumer_offsets-32 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,764] INFO [Log partition=__consumer_offsets-3, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,765] INFO Created log for partition __consumer_offsets-3 in /export/server/kafka/logs/__consumer_offsets-3 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,765] INFO [Partition __consumer_offsets-3 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-3 (kafka.cluster.Partition)
[2025-06-13 11:02:42,765] INFO [Partition __consumer_offsets-3 broker=0] Log loaded for partition __consumer_offsets-3 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,776] INFO [Log partition=__consumer_offsets-13, dir=/export/server/kafka/logs] Loading producer state till offset 0 with message format version 2 (kafka.log.Log)
[2025-06-13 11:02:42,777] INFO Created log for partition __consumer_offsets-13 in /export/server/kafka/logs/__consumer_offsets-13 with properties {cleanup.policy=compact, compression.type="producer", segment.bytes=104857600} (kafka.log.LogManager)
[2025-06-13 11:02:42,777] INFO [Partition __consumer_offsets-13 broker=0] No checkpointed highwatermark is found for partition __consumer_offsets-13 (kafka.cluster.Partition)
[2025-06-13 11:02:42,777] INFO [Partition __consumer_offsets-13 broker=0] Log loaded for partition __consumer_offsets-13 with initial high watermark 0 (kafka.cluster.Partition)
[2025-06-13 11:02:42,784] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 22 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,785] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-22 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,786] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 25 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,786] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-25 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,786] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 28 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,786] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-28 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,786] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 31 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,786] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-31 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,786] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 34 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,786] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-34 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 37 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-37 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 40 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-40 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 43 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-43 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 46 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-46 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 49 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-49 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 41 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-41 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 44 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-44 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 47 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-47 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 1 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-1 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 4 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-4 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 7 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-7 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 10 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-10 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 13 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-13 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,787] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 16 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,787] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-16 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 19 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-19 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 2 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-2 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 5 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-5 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 8 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-8 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 11 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-11 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 14 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-14 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 17 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-17 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 20 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-20 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 23 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-23 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 26 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-26 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 29 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-29 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 32 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-32 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 35 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-35 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 38 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,788] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-38 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,788] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 0 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-0 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 3 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-3 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 6 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-6 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 9 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-9 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 12 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-12 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 15 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-15 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 18 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-18 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 21 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-21 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 24 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-24 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 27 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-27 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 30 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-30 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 33 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-33 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 36 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-36 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 39 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-39 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 42 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-42 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,789] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 45 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,789] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-45 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,790] INFO [GroupCoordinator 0]: Elected as the group coordinator for partition 48 in epoch 0 (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,790] INFO [GroupMetadataManager brokerId=0] Scheduling loading of offsets and group metadata from __consumer_offsets-48 for epoch 0 (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,796] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-22 in 10 milliseconds for epoch 0, of which 4 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,798] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-25 in 12 milliseconds for epoch 0, of which 12 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,799] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-28 in 13 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,799] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-31 in 13 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,799] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-34 in 12 milliseconds for epoch 0, of which 12 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,800] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-37 in 13 milliseconds for epoch 0, of which 12 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,800] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-40 in 13 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,800] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-43 in 13 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,800] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-46 in 13 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,801] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-49 in 14 milliseconds for epoch 0, of which 13 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,801] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-41 in 14 milliseconds for epoch 0, of which 14 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,802] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-44 in 15 milliseconds for epoch 0, of which 15 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,802] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-47 in 15 milliseconds for epoch 0, of which 15 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,803] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-1 in 16 milliseconds for epoch 0, of which 16 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,803] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-4 in 16 milliseconds for epoch 0, of which 16 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,804] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-7 in 17 milliseconds for epoch 0, of which 16 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,805] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-10 in 18 milliseconds for epoch 0, of which 17 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,805] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-13 in 18 milliseconds for epoch 0, of which 18 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,805] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-16 in 17 milliseconds for epoch 0, of which 17 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,805] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-19 in 17 milliseconds for epoch 0, of which 17 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,805] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-2 in 17 milliseconds for epoch 0, of which 17 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,806] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-5 in 18 milliseconds for epoch 0, of which 17 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,806] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-8 in 18 milliseconds for epoch 0, of which 18 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,807] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-11 in 19 milliseconds for epoch 0, of which 19 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,807] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-14 in 19 milliseconds for epoch 0, of which 19 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,808] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-17 in 20 milliseconds for epoch 0, of which 19 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,808] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-20 in 20 milliseconds for epoch 0, of which 20 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,809] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-23 in 21 milliseconds for epoch 0, of which 20 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,809] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-26 in 21 milliseconds for epoch 0, of which 21 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,810] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-29 in 22 milliseconds for epoch 0, of which 21 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,810] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-32 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,810] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-35 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,810] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-38 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,810] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-0 in 21 milliseconds for epoch 0, of which 21 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,811] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-3 in 22 milliseconds for epoch 0, of which 21 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,811] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-6 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,811] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-9 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,811] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-12 in 22 milliseconds for epoch 0, of which 22 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,812] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-15 in 23 milliseconds for epoch 0, of which 23 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,812] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-18 in 23 milliseconds for epoch 0, of which 23 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,812] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-21 in 23 milliseconds for epoch 0, of which 23 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,813] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-24 in 24 milliseconds for epoch 0, of which 24 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,813] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-27 in 24 milliseconds for epoch 0, of which 24 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,813] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-30 in 24 milliseconds for epoch 0, of which 24 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,814] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-33 in 25 milliseconds for epoch 0, of which 25 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,814] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-36 in 25 milliseconds for epoch 0, of which 25 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,814] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-39 in 25 milliseconds for epoch 0, of which 25 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,814] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-42 in 25 milliseconds for epoch 0, of which 25 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,814] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-45 in 25 milliseconds for epoch 0, of which 25 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,815] INFO [GroupMetadataManager brokerId=0] Finished loading offsets and group metadata from __consumer_offsets-48 in 25 milliseconds for epoch 0, of which 24 milliseconds was spent in the scheduler. (kafka.coordinator.group.GroupMetadataManager)
[2025-06-13 11:02:42,900] INFO [GroupCoordinator 0]: Preparing to rebalance group realtime_group_a in state PreparingRebalance with old generation 0 (__consumer_offsets-11) (reason: Adding new member consumer-realtime_group_a-3-3edf4412-b9a8-4e2f-8d52-2898a0b21e06 with group instance id None) (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,924] INFO [GroupCoordinator 0]: Stabilized group realtime_group_a generation 1 (__consumer_offsets-11) with 4 members (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:02:42,952] INFO [GroupCoordinator 0]: Assignment received from leader for group realtime_group_a for generation 1. The group has 4 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,127] INFO [GroupCoordinator 0]: Member[group.instance.id None, member.id consumer-realtime_group_a-1-c4fb1b4a-a225-4cce-af7c-d8c7f19e46aa] in group realtime_group_a has left, removing it from the group (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,129] INFO [GroupCoordinator 0]: Preparing to rebalance group realtime_group_a in state PreparingRebalance with old generation 1 (__consumer_offsets-11) (reason: removing member consumer-realtime_group_a-1-c4fb1b4a-a225-4cce-af7c-d8c7f19e46aa on LeaveGroup) (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,185] INFO [GroupCoordinator 0]: Member[group.instance.id None, member.id consumer-realtime_group_a-2-de9a84e5-59c1-441e-a00c-1e5cbf2bfa8e] in group realtime_group_a has left, removing it from the group (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,213] INFO [GroupCoordinator 0]: Member[group.instance.id None, member.id consumer-realtime_group_a-3-3edf4412-b9a8-4e2f-8d52-2898a0b21e06] in group realtime_group_a has left, removing it from the group (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,242] INFO [GroupCoordinator 0]: Member[group.instance.id None, member.id consumer-realtime_group_a-4-bc96e885-f577-4304-9e41-11ace2e38907] in group realtime_group_a has left, removing it from the group (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:32,244] INFO [GroupCoordinator 0]: Group realtime_group_a with generation 2 is now empty (__consumer_offsets-11) (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:52,546] INFO [GroupCoordinator 0]: Preparing to rebalance group realtime_group_a in state PreparingRebalance with old generation 2 (__consumer_offsets-11) (reason: Adding new member consumer-realtime_group_a-3-b3338eb3-5f9a-449b-85fc-886987e33186 with group instance id None) (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:52,548] INFO [GroupCoordinator 0]: Stabilized group realtime_group_a generation 3 (__consumer_offsets-11) with 1 members (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:52,553] INFO [GroupCoordinator 0]: Preparing to rebalance group realtime_group_a in state PreparingRebalance with old generation 3 (__consumer_offsets-11) (reason: Adding new member consumer-realtime_group_a-1-2621db46-ec55-4e70-94b3-24a4edcb7ac8 with group instance id None) (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:52,570] INFO [GroupCoordinator 0]: Stabilized group realtime_group_a generation 4 (__consumer_offsets-11) with 4 members (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:03:52,585] INFO [GroupCoordinator 0]: Assignment received from leader for group realtime_group_a for generation 4. The group has 4 members, 0 of which are static. (kafka.coordinator.group.GroupCoordinator)
[2025-06-13 11:04:54,602] ERROR Error while reading checkpoint file /export/server/kafka/logs/cleaner-offset-checkpoint (kafka.server.LogDirFailureChannel)
java.nio.file.NoSuchFileException: /export/server/kafka/logs/cleaner-offset-checkpoint
	at sun.nio.fs.UnixException.translateToIOException(UnixException.java:86)
	at sun.nio.fs.UnixException.rethrowAsIOException(UnixException.java:102)
	at sun.nio.fs.UnixException.rethrowAsIOException(UnixException.java:107)
	at sun.nio.fs.UnixFileSystemProvider.newByteChannel(UnixFileSystemProvider.java:214)
	at java.nio.file.Files.newByteChannel(Files.java:361)
	at java.nio.file.Files.newByteChannel(Files.java:407)
	at java.nio.file.spi.FileSystemProvider.newInputStream(FileSystemProvider.java:384)
	at java.nio.file.Files.newInputStream(Files.java:152)
	at java.nio.file.Files.newBufferedReader(Files.java:2784)
	at java.nio.file.Files.newBufferedReader(Files.java:2816)
	at kafka.server.checkpoints.CheckpointFile.liftedTree2$1(CheckpointFile.scala:127)
	at kafka.server.checkpoints.CheckpointFile.read(CheckpointFile.scala:126)
	at kafka.server.checkpoints.OffsetCheckpointFile.read(OffsetCheckpointFile.scala:69)
	at kafka.log.LogCleanerManager.$anonfun$allCleanerCheckpoints$2(LogCleanerManager.scala:134)
	at scala.collection.TraversableLike.$anonfun$flatMap$1(TraversableLike.scala:293)
	at scala.collection.Iterator.foreach(Iterator.scala:943)
	at scala.collection.Iterator.foreach$(Iterator.scala:943)
	at scala.collection.AbstractIterator.foreach(Iterator.scala:1431)
	at scala.collection.MapLike$DefaultValuesIterable.foreach(MapLike.scala:213)
	at scala.collection.TraversableLike.flatMap(TraversableLike.scala:293)
	at scala.collection.TraversableLike.flatMap$(TraversableLike.scala:290)
	at scala.collection.AbstractTraversable.flatMap(Traversable.scala:108)
	at kafka.log.LogCleanerManager.$anonfun$allCleanerCheckpoints$1(LogCleanerManager.scala:132)
	at kafka.log.LogCleanerManager.allCleanerCheckpoints(LogCleanerManager.scala:140)
	at kafka.log.LogCleanerManager.$anonfun$grabFilthiestCompactedLog$1(LogCleanerManager.scala:171)
	at kafka.log.LogCleanerManager.grabFilthiestCompactedLog(LogCleanerManager.scala:168)
	at kafka.log.LogCleaner$CleanerThread.cleanFilthiestLog(LogCleaner.scala:345)
	at kafka.log.LogCleaner$CleanerThread.tryCleanFilthiestLog(LogCleaner.scala:332)
	at kafka.log.LogCleaner$CleanerThread.doWork(LogCleaner.scala:321)
	at kafka.utils.ShutdownableThread.run(ShutdownableThread.scala:96)
[2025-06-13 11:04:54,605] WARN [ReplicaManager broker=0] Stopping serving replicas in dir /export/server/kafka/logs (kafka.server.ReplicaManager)
[2025-06-13 11:04:54,607] INFO [ReplicaFetcherManager on broker 0] Removed fetcher for partitions Set(__consumer_offsets-22, __consumer_offsets-30, __consumer_offsets-8, __consumer_offsets-21, __consumer_offsets-4, __consumer_offsets-27, __consumer_offsets-7, __consumer_offsets-9, __consumer_offsets-46, __consumer_offsets-25, __consumer_offsets-35, __consumer_offsets-41, __consumer_offsets-33, __consumer_offsets-23, __consumer_offsets-49, __consumer_offsets-47, __consumer_offsets-16, __consumer_offsets-28, attendance-1, __consumer_offsets-31, __consumer_offsets-36, attendance-2, __consumer_offsets-42, __consumer_offsets-3, __consumer_offsets-18, __consumer_offsets-37, __consumer_offsets-15, __consumer_offsets-24, attendance-0, __consumer_offsets-38, __consumer_offsets-17, __consumer_offsets-48, __consumer_offsets-19, __consumer_offsets-11, __consumer_offsets-13, __consumer_offsets-2, __consumer_offsets-43, __consumer_offsets-6, __consumer_offsets-14, __consumer_offsets-20, __consumer_offsets-0, __consumer_offsets-44, __consumer_offsets-39, __consumer_offsets-12, __consumer_offsets-45, __consumer_offsets-1, __consumer_offsets-5, __consumer_offsets-26, __consumer_offsets-29, __consumer_offsets-34, __consumer_offsets-10, __consumer_offsets-32, __consumer_offsets-40) (kafka.server.ReplicaFetcherManager)
[2025-06-13 11:04:54,608] INFO [ReplicaAlterLogDirsManager on broker 0] Removed fetcher for partitions Set(__consumer_offsets-22, __consumer_offsets-30, __consumer_offsets-8, __consumer_offsets-21, __consumer_offsets-4, __consumer_offsets-27, __consumer_offsets-7, __consumer_offsets-9, __consumer_offsets-46, __consumer_offsets-25, __consumer_offsets-35, __consumer_offsets-41, __consumer_offsets-33, __consumer_offsets-23, __consumer_offsets-49, __consumer_offsets-47, __consumer_offsets-16, __consumer_offsets-28, attendance-1, __consumer_offsets-31, __consumer_offsets-36, attendance-2, __consumer_offsets-42, __consumer_offsets-3, __consumer_offsets-18, __consumer_offsets-37, __consumer_offsets-15, __consumer_offsets-24, attendance-0, __consumer_offsets-38, __consumer_offsets-17, __consumer_offsets-48, __consumer_offsets-19, __consumer_offsets-11, __consumer_offsets-13, __consumer_offsets-2, __consumer_offsets-43, __consumer_offsets-6, __consumer_offsets-14, __consumer_offsets-20, __consumer_offsets-0, __consumer_offsets-44, __consumer_offsets-39, __consumer_offsets-12, __consumer_offsets-45, __consumer_offsets-1, __consumer_offsets-5, __consumer_offsets-26, __consumer_offsets-29, __consumer_offsets-34, __consumer_offsets-10, __consumer_offsets-32, __consumer_offsets-40) (kafka.server.ReplicaAlterLogDirsManager)
[2025-06-13 11:04:54,640] WARN [ReplicaManager broker=0] Broker 0 stopped fetcher for partitions __consumer_offsets-22,__consumer_offsets-30,__consumer_offsets-8,__consumer_offsets-21,__consumer_offsets-4,__consumer_offsets-27,__consumer_offsets-7,__consumer_offsets-9,__consumer_offsets-46,__consumer_offsets-25,__consumer_offsets-35,__consumer_offsets-41,__consumer_offsets-33,__consumer_offsets-23,__consumer_offsets-49,__consumer_offsets-47,__consumer_offsets-16,__consumer_offsets-28,attendance-1,__consumer_offsets-31,__consumer_offsets-36,attendance-2,__consumer_offsets-42,__consumer_offsets-3,__consumer_offsets-18,__consumer_offsets-37,__consumer_offsets-15,__consumer_offsets-24,attendance-0,__consumer_offsets-38,__consumer_offsets-17,__consumer_offsets-48,__consumer_offsets-19,__consumer_offsets-11,__consumer_offsets-13,__consumer_offsets-2,__consumer_offsets-43,__consumer_offsets-6,__consumer_offsets-14,__consumer_offsets-20,__consumer_offsets-0,__consumer_offsets-44,__consumer_offsets-39,__consumer_offsets-12,__consumer_offsets-45,__consumer_offsets-1,__consumer_offsets-5,__consumer_offsets-26,__consumer_offsets-29,__consumer_offsets-34,__consumer_offsets-10,__consumer_offsets-32,__consumer_offsets-40 and stopped moving logs for partitions  because they are in the failed log directory /export/server/kafka/logs. (kafka.server.ReplicaManager)
[2025-06-13 11:04:54,641] WARN Stopping serving logs in dir /export/server/kafka/logs (kafka.log.LogManager)
[2025-06-13 11:04:54,643] ERROR Shutdown broker because all log dirs in /export/server/kafka/logs have failed (kafka.log.LogManager)
