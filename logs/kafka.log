[2025-06-13 10:59:47,389] INFO Registered kafka:type=kafka.Log4jController MBean (kafka.utils.Log4jControllerRegistration$)
[2025-06-13 10:59:48,406] INFO Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation (org.apache.zookeeper.common.X509Util)
[2025-06-13 10:59:48,599] INFO Registered signal handlers for TERM, INT, HUP (org.apache.kafka.common.utils.LoggingSignalHandler)
[2025-06-13 10:59:48,609] INFO starting (kafka.server.KafkaServer)
[2025-06-13 10:59:48,610] INFO Connecting to zookeeper on node1:2181 (kafka.server.KafkaServer)
[2025-06-13 10:59:48,657] INFO [ZooKeeperClient Kafka server] Initializing a new session to node1:2181. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 10:59:48,670] INFO Client environment:zookeeper.version=3.5.9-83df9301aa5c2a5d284a9940177808c01bc35cef, built on 01/06/2021 20:03 GMT (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,670] INFO Client environment:host.name=localhost (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,670] INFO Client environment:java.version=1.8.0_452 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,671] INFO Client environment:java.vendor=Private Build (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,671] INFO Client environment:java.home=/usr/lib/jvm/java-8-openjdk-amd64/jre (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,671] INFO Client environment:java.class.path=.:/export/server/jdk8/lib/dt.jar:/export/server/jdk8/lib/tools.jar:/export/server/kafka/bin/../libs/activation-1.1.1.jar:/export/server/kafka/bin/../libs/aopalliance-repackaged-2.6.1.jar:/export/server/kafka/bin/../libs/argparse4j-0.7.0.jar:/export/server/kafka/bin/../libs/audience-annotations-0.5.0.jar:/export/server/kafka/bin/../libs/commons-cli-1.4.jar:/export/server/kafka/bin/../libs/commons-lang3-3.8.1.jar:/export/server/kafka/bin/../libs/connect-api-2.8.2.jar:/export/server/kafka/bin/../libs/connect-basic-auth-extension-2.8.2.jar:/export/server/kafka/bin/../libs/connect-file-2.8.2.jar:/export/server/kafka/bin/../libs/connect-json-2.8.2.jar:/export/server/kafka/bin/../libs/connect-mirror-2.8.2.jar:/export/server/kafka/bin/../libs/connect-mirror-client-2.8.2.jar:/export/server/kafka/bin/../libs/connect-runtime-2.8.2.jar:/export/server/kafka/bin/../libs/connect-transforms-2.8.2.jar:/export/server/kafka/bin/../libs/hk2-api-2.6.1.jar:/export/server/kafka/bin/../libs/hk2-locator-2.6.1.jar:/export/server/kafka/bin/../libs/hk2-utils-2.6.1.jar:/export/server/kafka/bin/../libs/jackson-annotations-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-core-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-databind-2.10.5.1.jar:/export/server/kafka/bin/../libs/jackson-dataformat-csv-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-datatype-jdk8-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-jaxrs-base-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-jaxrs-json-provider-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-jaxb-annotations-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-paranamer-2.10.5.jar:/export/server/kafka/bin/../libs/jackson-module-scala_2.12-2.10.5.jar:/export/server/kafka/bin/../libs/jakarta.activation-api-1.2.1.jar:/export/server/kafka/bin/../libs/jakarta.annotation-api-1.3.5.jar:/export/server/kafka/bin/../libs/jakarta.inject-2.6.1.jar:/export/server/kafka/bin/../libs/jakarta.validation-api-2.0.2.jar:/export/server/kafka/bin/../libs/jakarta.ws.rs-api-2.1.6.jar:/export/server/kafka/bin/../libs/jakarta.xml.bind-api-2.3.2.jar:/export/server/kafka/bin/../libs/javassist-3.27.0-GA.jar:/export/server/kafka/bin/../libs/javax.servlet-api-3.1.0.jar:/export/server/kafka/bin/../libs/javax.ws.rs-api-2.1.1.jar:/export/server/kafka/bin/../libs/jaxb-api-2.3.0.jar:/export/server/kafka/bin/../libs/jersey-client-2.34.jar:/export/server/kafka/bin/../libs/jersey-common-2.34.jar:/export/server/kafka/bin/../libs/jersey-container-servlet-2.34.jar:/export/server/kafka/bin/../libs/jersey-container-servlet-core-2.34.jar:/export/server/kafka/bin/../libs/jersey-hk2-2.34.jar:/export/server/kafka/bin/../libs/jersey-server-2.34.jar:/export/server/kafka/bin/../libs/jetty-client-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-continuation-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-http-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-io-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-security-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-server-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-servlet-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-servlets-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-util-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jetty-util-ajax-9.4.48.v20220622.jar:/export/server/kafka/bin/../libs/jline-3.12.1.jar:/export/server/kafka/bin/../libs/jopt-simple-5.0.4.jar:/export/server/kafka/bin/../libs/kafka_2.12-2.8.2.jar:/export/server/kafka/bin/../libs/kafka_2.12-2.8.2-sources.jar:/export/server/kafka/bin/../libs/kafka-clients-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-log4j-appender-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-metadata-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-raft-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-shell-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-examples-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-scala_2.12-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-streams-test-utils-2.8.2.jar:/export/server/kafka/bin/../libs/kafka-tools-2.8.2.jar:/export/server/kafka/bin/../libs/log4j-1.2.17.jar:/export/server/kafka/bin/../libs/lz4-java-1.7.1.jar:/export/server/kafka/bin/../libs/maven-artifact-3.8.1.jar:/export/server/kafka/bin/../libs/metrics-core-2.2.0.jar:/export/server/kafka/bin/../libs/netty-buffer-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-codec-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-common-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-handler-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-resolver-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-tcnative-classes-2.0.46.Final.jar:/export/server/kafka/bin/../libs/netty-transport-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-classes-epoll-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-native-epoll-4.1.73.Final.jar:/export/server/kafka/bin/../libs/netty-transport-native-unix-common-4.1.73.Final.jar:/export/server/kafka/bin/../libs/osgi-resource-locator-1.0.3.jar:/export/server/kafka/bin/../libs/paranamer-2.8.jar:/export/server/kafka/bin/../libs/plexus-utils-3.2.1.jar:/export/server/kafka/bin/../libs/reflections-0.9.12.jar:/export/server/kafka/bin/../libs/rocksdbjni-5.18.4.jar:/export/server/kafka/bin/../libs/scala-collection-compat_2.12-2.3.0.jar:/export/server/kafka/bin/../libs/scala-java8-compat_2.12-0.9.1.jar:/export/server/kafka/bin/../libs/scala-library-2.12.13.jar:/export/server/kafka/bin/../libs/scala-logging_2.12-3.9.2.jar:/export/server/kafka/bin/../libs/scala-reflect-2.12.13.jar:/export/server/kafka/bin/../libs/slf4j-api-1.7.30.jar:/export/server/kafka/bin/../libs/slf4j-log4j12-1.7.30.jar:/export/server/kafka/bin/../libs/snappy-java-1.1.8.1.jar:/export/server/kafka/bin/../libs/zookeeper-3.5.9.jar:/export/server/kafka/bin/../libs/zookeeper-jute-3.5.9.jar:/export/server/kafka/bin/../libs/zstd-jni-1.4.9-1.jar (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,672] INFO Client environment:java.library.path=/usr/java/packages/lib/amd64:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,672] INFO Client environment:java.io.tmpdir=/tmp (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,672] INFO Client environment:java.compiler=<NA> (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,672] INFO Client environment:os.name=Linux (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:os.arch=amd64 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:os.version=6.11.0-26-generic (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:user.name=root (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:user.home=/root (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:user.dir=/root/job14 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:os.memory.free=1009MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,673] INFO Client environment:os.memory.max=1024MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,674] INFO Client environment:os.memory.total=1024MB (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,679] INFO Initiating client connection, connectString=node1:2181 sessionTimeout=18000 watcher=kafka.zookeeper.ZooKeeperClient$ZooKeeperClientWatcher$@6b2ea799 (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:48,690] INFO jute.maxbuffer value is 4194304 Bytes (org.apache.zookeeper.ClientCnxnSocket)
[2025-06-13 10:59:48,702] INFO zookeeper.request.timeout value is 0. feature enabled= (org.apache.zookeeper.ClientCnxn)
[2025-06-13 10:59:48,708] INFO [ZooKeeperClient Kafka server] Waiting until connected. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 10:59:48,715] INFO Opening socket connection to server node1/127.0.0.1:2181. Will not attempt to authenticate using SASL (unknown error) (org.apache.zookeeper.ClientCnxn)
[2025-06-13 10:59:48,722] INFO Socket connection established, initiating session, client: /127.0.0.1:42774, server: node1/127.0.0.1:2181 (org.apache.zookeeper.ClientCnxn)
[2025-06-13 10:59:48,752] INFO Session establishment complete on server node1/127.0.0.1:2181, sessionid = 0x100004f5f180000, negotiated timeout = 18000 (org.apache.zookeeper.ClientCnxn)
[2025-06-13 10:59:48,761] INFO [ZooKeeperClient Kafka server] Connected. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 10:59:49,038] INFO [feature-zk-node-event-process-thread]: Starting (kafka.server.FinalizedFeatureChangeListener$ChangeNotificationProcessorThread)
[2025-06-13 10:59:49,063] INFO Feature ZK node at path: /feature does not exist (kafka.server.FinalizedFeatureChangeListener)
[2025-06-13 10:59:49,064] INFO Cleared cache (kafka.server.FinalizedFeatureCache)
[2025-06-13 10:59:49,478] INFO Cluster ID = iiC77VKpQGi25v7rIpF1nw (kafka.server.KafkaServer)
[2025-06-13 10:59:49,499] ERROR Fatal error during KafkaServer startup. Prepare to shutdown (kafka.server.KafkaServer)
kafka.common.InconsistentClusterIdException: The Cluster ID iiC77VKpQGi25v7rIpF1nw doesn't match stored clusterId Some(tl7xkvfJTiWgePUCLciJrg) in meta.properties. The broker is trying to join the wrong cluster. Configured zookeeper.connect may be wrong.
	at kafka.server.KafkaServer.startup(KafkaServer.scala:218)
	at kafka.Kafka$.main(Kafka.scala:109)
	at kafka.Kafka.main(Kafka.scala)
[2025-06-13 10:59:49,503] INFO shutting down (kafka.server.KafkaServer)
[2025-06-13 10:59:49,510] INFO [feature-zk-node-event-process-thread]: Shutting down (kafka.server.FinalizedFeatureChangeListener$ChangeNotificationProcessorThread)
[2025-06-13 10:59:49,511] INFO [feature-zk-node-event-process-thread]: Shutdown completed (kafka.server.FinalizedFeatureChangeListener$ChangeNotificationProcessorThread)
[2025-06-13 10:59:49,512] INFO [feature-zk-node-event-process-thread]: Stopped (kafka.server.FinalizedFeatureChangeListener$ChangeNotificationProcessorThread)
[2025-06-13 10:59:49,513] INFO [ZooKeeperClient Kafka server] Closing. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 10:59:49,626] INFO Session: 0x100004f5f180000 closed (org.apache.zookeeper.ZooKeeper)
[2025-06-13 10:59:49,626] INFO EventThread shut down for session: 0x100004f5f180000 (org.apache.zookeeper.ClientCnxn)
[2025-06-13 10:59:49,629] INFO [ZooKeeperClient Kafka server] Closed. (kafka.zookeeper.ZooKeeperClient)
[2025-06-13 10:59:49,636] INFO App info kafka.server for 0 unregistered (org.apache.kafka.common.utils.AppInfoParser)
[2025-06-13 10:59:49,637] INFO shut down completed (kafka.server.KafkaServer)
[2025-06-13 10:59:49,638] ERROR Exiting Kafka. (kafka.Kafka$)
[2025-06-13 10:59:49,639] INFO shutting down (kafka.server.KafkaServer)
