SLF4J: Class path contains multiple SLF4J bindings.
SLF4J: Found binding in [jar:file:/export/server/spark-3.1.3-bin-hadoop3.2/jars/slf4j-log4j12-1.7.30.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: Found binding in [jar:file:/export/server/hadoop-3.3.6/share/hadoop/common/lib/slf4j-reload4j-1.7.36.jar!/org/slf4j/impl/StaticLoggerBinder.class]
SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J: Actual binding is of type [org.slf4j.impl.Log4jLoggerFactory]
2025-06-13 11:02:31,884 WARN util.NativeCodeLoader: Unable to load native-hadoop library for your platform... using builtin-java classes where applicable
Setting default log level to "WARN".
To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).
2025-06-13 11:02:36,133 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 11:02:39,593 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-d7c3030b-86ee-4b82-a584-f78f8acc1c68. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 11:02:39,594 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 11:02:40,971 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 11:02:41,198 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-0cbfe57a-d715-4a0c-84fa-8ce7078821a9. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 11:02:41,199 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 11:02:41,292 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 11:02:41,471 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-7f74f9fa-a7a7-4e2b-9c88-89964d1a85d1. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 11:02:41,471 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 11:02:41,537 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    
2025-06-13 11:02:41,709 WARN streaming.StreamingQueryManager: Temporary checkpoint location created which is deleted normally when the query didn't fail: /tmp/temporary-b93da4ca-6f4e-4247-8722-563123a4563b. If it's required to delete it under any circumstances, please set spark.sql.streaming.forceDeleteTempCheckpointLocation to true. Important to know deleting temp checkpoint folder is best effort.
2025-06-13 11:02:41,709 WARN streaming.StreamingQueryManager: spark.sql.adaptive.enabled is not supported in streaming DataFrames/Datasets and will be disabled.
2025-06-13 11:02:41,768 WARN kafka010.KafkaSourceProvider: Kafka option 'kafka.group.id' has been set on this query, it is
 not recommended to set this option. This option is unsafe to use since multiple concurrent
 queries or sources using the same group id will interfere with each other as they are part
 of the same consumer group. Restarted queries may also suffer interference from the
 previous run having the same group id. The user should have only one query per group id,
 and/or set the option 'kafka.session.timeout.ms' to be very small so that the Kafka
 consumers from the previous query are marked dead by the Kafka group coordinator before the
 restarted query starts running.
    

[Stage 0:>                                                          (0 + 1) / 1]

[Stage 0:>    (0 + 1) / 1][Stage 2:> (0 + 23) / 200][Stage 4:>    (0 + 0) / 1]

[Stage 0:>    (0 + 1) / 1][Stage 2:>(25 + 23) / 200][Stage 4:>    (0 + 0) / 1]

[Stage 0:>    (0 + 1) / 1][Stage 2:>(31 + 23) / 200][Stage 4:>    (0 + 0) / 1]

[Stage 0:>    (0 + 1) / 1][Stage 2:>(47 + 23) / 200][Stage 4:>    (0 + 0) / 1]

[Stage 0:>    (0 + 1) / 1][Stage 2:>(63 + 24) / 200][Stage 4:>    (0 + 0) / 1]
2025-06-13 11:02:48,659 WARN hdfs.DataStreamer: Caught exception
java.lang.InterruptedException
	at java.lang.Object.wait(Native Method)
	at java.lang.Thread.join(Thread.java:1257)
	at java.lang.Thread.join(Thread.java:1331)
	at org.apache.hadoop.hdfs.DataStreamer.closeResponder(DataStreamer.java:986)
	at org.apache.hadoop.hdfs.DataStreamer.endBlock(DataStreamer.java:640)
	at org.apache.hadoop.hdfs.DataStreamer.run(DataStreamer.java:810)

[Stage 2:>(82 + 24) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

                                                                                
2025-06-13 11:02:48,798 WARN hdfs.DataStreamer: Caught exception
java.lang.InterruptedException
	at java.lang.Object.wait(Native Method)
	at java.lang.Thread.join(Thread.java:1257)
	at java.lang.Thread.join(Thread.java:1331)
	at org.apache.hadoop.hdfs.DataStreamer.closeResponder(DataStreamer.java:986)
	at org.apache.hadoop.hdfs.DataStreamer.endBlock(DataStreamer.java:640)
	at org.apache.hadoop.hdfs.DataStreamer.run(DataStreamer.java:810)

[Stage 2:(121 + 24) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

[Stage 2:(136 + 24) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

[Stage 2:(146 + 26) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

[Stage 2:(160 + 24) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

[Stage 2:(175 + 24) / 200][Stage 4:>    (0 + 0) / 1][Stage 6:>  (0 + 0) / 200]

[Stage 2:>(194 + 6) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:> (0 + 17) / 200]

[Stage 2:>(198 + 2) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:> (1 + 21) / 200]

[Stage 2:>(199 + 1) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:> (1 + 22) / 200]

[Stage 2:>(199 + 1) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:> (5 + 22) / 200]

[Stage 2:>(199 + 1) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:>(19 + 22) / 200]

[Stage 2:>(199 + 1) / 200][Stage 4:>    (0 + 1) / 1][Stage 6:>(32 + 22) / 200]

                                                                                
2025-06-13 11:02:51,668 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 10371 milliseconds
2025-06-13 11:02:51,722 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 10182 milliseconds

[Stage 6:=========================>                             (91 + 24) / 200]

[Stage 6:============================>                         (104 + 24) / 200]
2025-06-13 11:02:52,400 WARN hdfs.DataStreamer: Caught exception
java.lang.InterruptedException
	at java.lang.Object.wait(Native Method)
	at java.lang.Thread.join(Thread.java:1257)
	at java.lang.Thread.join(Thread.java:1331)
	at org.apache.hadoop.hdfs.DataStreamer.closeResponder(DataStreamer.java:986)
	at org.apache.hadoop.hdfs.DataStreamer.endBlock(DataStreamer.java:640)
	at org.apache.hadoop.hdfs.DataStreamer.run(DataStreamer.java:810)

[Stage 6:================================>                     (120 + 24) / 200]

[Stage 6:====================================>                 (136 + 24) / 200]

[Stage 6:======================================>               (143 + 24) / 200]

[Stage 6:==========================================>           (156 + 24) / 200]

[Stage 6:============================================>         (164 + 24) / 200]

[Stage 6:============> (173 + 24) / 200][Stage 7:>                  (0 + 0) / 1]

[Stage 6:=============>(187 + 13) / 200][Stage 7:>                  (0 + 1) / 1]

[Stage 6:==============>(194 + 6) / 200][Stage 7:>                  (0 + 1) / 1]

[Stage 6:==============>(198 + 2) / 200][Stage 7:>                  (0 + 1) / 1]

                                                                                
2025-06-13 11:02:54,280 WARN streaming.ProcessingTimeExecutor: Current batch is falling behind. The trigger interval is 10000 milliseconds, but spent 12509 milliseconds

[Stage 8:===========>                                           (43 + 24) / 200]

[Stage 8:================>                                      (60 + 24) / 200]

[Stage 8:======================>                                (81 + 24) / 200]

[Stage 8:============================>                         (104 + 24) / 200]

[Stage 8:====================================>                 (136 + 25) / 200]

[Stage 8:==========================================>           (159 + 24) / 200]

[Stage 8:==================================================>   (187 + 13) / 200]

[Stage 8:===================================================>  (190 + 10) / 200]

[Stage 8:======================================================>(198 + 2) / 200]

                                                                                

[Stage 9:>                                                          (0 + 1) / 1]

                                                                                

[Stage 10:>                                                         (0 + 1) / 1]

[Stage 11:==================>                                   (70 + 24) / 200]

[Stage 11:=========================>                            (95 + 24) / 200]

[Stage 11:==============================>                      (116 + 24) / 200]

[Stage 11:====================================>                (139 + 24) / 200]

[Stage 11:===============================================>     (179 + 21) / 200]

[Stage 11:=================================================>   (188 + 12) / 200]

[Stage 11:====================================================> (196 + 4) / 200]

                                                                                

[Stage 14:====================>                                 (75 + 24) / 200]

[Stage 14:===========================>                         (103 + 24) / 200]

[Stage 14:==============================>                      (115 + 24) / 200]

[Stage 14:======================================>              (144 + 24) / 200]

[Stage 14:==========================================>          (161 + 24) / 200]

[Stage 14:===============================================>     (178 + 22) / 200]

[Stage 14:====================================================> (193 + 7) / 200]

                                                                                

[Stage 15:>                                                         (0 + 1) / 1]

                                                                                
2025-06-13 11:03:32,190 WARN scheduler.TaskSetManager: Lost task 127.0 in stage 17.0 (TID 1136) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,190 WARN scheduler.TaskSetManager: Lost task 92.0 in stage 17.0 (TID 1101) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,192 WARN scheduler.TaskSetManager: Lost task 96.0 in stage 17.0 (TID 1105) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,193 WARN scheduler.TaskSetManager: Lost task 128.0 in stage 17.0 (TID 1137) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,193 WARN scheduler.TaskSetManager: Lost task 99.0 in stage 17.0 (TID 1108) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,193 WARN scheduler.TaskSetManager: Lost task 129.0 in stage 17.0 (TID 1138) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,194 WARN scheduler.TaskSetManager: Lost task 97.0 in stage 17.0 (TID 1106) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,194 WARN scheduler.TaskSetManager: Lost task 95.0 in stage 17.0 (TID 1104) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,197 WARN scheduler.TaskSetManager: Lost task 100.0 in stage 17.0 (TID 1109) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,200 WARN hdfs.DFSClient: Caught exception 
java.lang.InterruptedException: sleep interrupted
	at java.lang.Thread.sleep(Native Method)
	at org.apache.hadoop.hdfs.DFSOutputStream.completeFile(DFSOutputStream.java:967)
	at org.apache.hadoop.hdfs.DFSOutputStream.completeFile(DFSOutputStream.java:907)
	at org.apache.hadoop.hdfs.DFSOutputStream.closeImpl(DFSOutputStream.java:890)
	at org.apache.hadoop.hdfs.DFSOutputStream.close(DFSOutputStream.java:845)
	at org.apache.hadoop.fs.FSDataOutputStream$PositionCache.close(FSDataOutputStream.java:72)
	at org.apache.hadoop.fs.FSDataOutputStream.close(FSDataOutputStream.java:101)
	at org.apache.spark.sql.execution.streaming.CheckpointFileManager$RenameBasedFSDataOutputStream.close(CheckpointFileManager.scala:145)
	at org.apache.spark.sql.execution.streaming.HDFSMetadataLog.$anonfun$addNewBatchByStream$2(HDFSMetadataLog.scala:176)
	at scala.runtime.java8.JFunction0$mcZ$sp.apply(JFunction0$mcZ$sp.java:23)
	at scala.Option.getOrElse(Option.scala:189)
	at org.apache.spark.sql.execution.streaming.HDFSMetadataLog.addNewBatchByStream(HDFSMetadataLog.scala:171)
	at org.apache.spark.sql.execution.streaming.HDFSMetadataLog.add(HDFSMetadataLog.scala:116)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runBatch$17(MicroBatchExecution.scala:601)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.withProgressLocked(MicroBatchExecution.scala:613)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runBatch(MicroBatchExecution.scala:598)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$2(MicroBatchExecution.scala:226)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken(ProgressReporter.scala:357)
	at org.apache.spark.sql.execution.streaming.ProgressReporter.reportTimeTaken$(ProgressReporter.scala:355)
	at org.apache.spark.sql.execution.streaming.StreamExecution.reportTimeTaken(StreamExecution.scala:68)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.$anonfun$runActivatedStream$1(MicroBatchExecution.scala:194)
	at org.apache.spark.sql.execution.streaming.ProcessingTimeExecutor.execute(TriggerExecutor.scala:57)
	at org.apache.spark.sql.execution.streaming.MicroBatchExecution.runActivatedStream(MicroBatchExecution.scala:188)
	at org.apache.spark.sql.execution.streaming.StreamExecution.$anonfun$runStream$1(StreamExecution.scala:334)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.streaming.StreamExecution.org$apache$spark$sql$execution$streaming$StreamExecution$$runStream(StreamExecution.scala:317)
	at org.apache.spark.sql.execution.streaming.StreamExecution$$anon$1.run(StreamExecution.scala:244)
启动Spark Streaming处理...
[2025-06-13 11:02:48.870615] 成功写入1条记录到raw_attendance_data
[2025-06-13 11:02:51.599398] 成功写入1条记录到attendance_summary
[2025-06-13 11:02:56.885894] 成功写入1条记录到class_attendance
[2025-06-13 11:03:01.811253] 成功写入1条记录到attendance_summary
[2025-06-13 11:03:12.961593] 成功写入3条记录到class_attendance
[2025-06-13 11:03:20.777248] 成功写入1条记录到raw_attendance_data
[2025-06-13 11:03:22.715087] 成功写入4条记录到class_attendance
[2025-06-13 11:03:31.693691] 成功写入1条记录到attendance_summary
停止流处理...
写入MySQL失败: An error occurred while calling o199.collectToPython.
: org.apache.spark.SparkException: Job 11 cancelled as part of cancellation of all jobs
	at org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:2303)
	at org.apache.spark.scheduler.DAGScheduler.handleJobCancellation(DAGScheduler.scala:2199)
	at org.apache.spark.scheduler.DAGScheduler.$anonfun$doCancelAllJobs$2(DAGScheduler.scala:1017)
	at scala.runtime.java8.JFunction1$mcVI$sp.apply(JFunction1$mcVI$sp.java:23)
	at scala.collection.mutable.HashSet.foreach(HashSet.scala:79)
	at org.apache.spark.scheduler.DAGScheduler.doCancelAllJobs(DAGScheduler.scala:1016)
	at org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:2455)
	at org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2432)
	at org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:2421)
	at org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:49)
	at org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:902)
	at org.apache.spark.SparkContext.runJob(SparkContext.scala:2196)
	at org.apache.spark.SparkContext.runJob(SparkContext.scala:2217)
	at org.apache.spark.SparkContext.runJob(SparkContext.scala:2236)
	at org.apache.spark.SparkContext.runJob(SparkContext.scala:2261)
	at org.apache.spark.rdd.RDD.$anonfun$collect$1(RDD.scala:1030)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:151)
	at org.apache.spark.rdd.RDDOperationScope$.withScope(RDDOperationScope.scala:112)
	at org.apache.spark.rdd.RDD.withScope(RDD.scala:414)
	at org.apache.spark.rdd.RDD.collect(RDD.scala:1029)
	at org.apache.spark.sql.execution.SparkPlan.executeCollect(SparkPlan.scala:390)
	at org.apache.spark.sql.Dataset.$anonfun$collectToPython$1(Dataset.scala:3532)
	at org.apache.spark.sql.Dataset.$anonfun$withAction$1(Dataset.scala:3700)
	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$5(SQLExecution.scala:103)
	at org.apache.spark.sql.execution.SQLExecution$.withSQLConfPropagated(SQLExecution.scala:163)
	at org.apache.spark.sql.execution.SQLExecution$.$anonfun$withNewExecutionId$1(SQLExecution.scala:90)
	at org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:775)
	at org.apache.spark.sql.execution.SQLExecution$.withNewExecutionId(SQLExecution.scala:64)
	at org.apache.spark.sql.Dataset.withAction(Dataset.scala:3698)
	at org.apache.spark.sql.Dataset.collectToPython(Dataset.scala:3529)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at py4j.reflection.MethodInvoker.invoke(MethodInvoker.java:244)
	at py4j.reflection.ReflectionEngine.invoke(ReflectionEngine.java:357)
	at py4j.Gateway.invoke(Gateway.java:282)
	at py4j.commands.AbstractCommand.invokeMethod(AbstractCommand.java:132)
	at py4j.commands.CallCommand.execute(CallCommand.java:79)
	at py4j.GatewayConnection.run(GatewayConnection.java:238)
	at java.lang.Thread.run(Thread.java:750)


[Stage 17:============================>                        (109 + 11) / 200]
2025-06-13 11:03:32,281 WARN scheduler.TaskSetManager: Lost task 103.0 in stage 17.0 (TID 1112) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,282 WARN scheduler.TaskSetManager: Lost task 104.0 in stage 17.0 (TID 1113) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,363 WARN scheduler.TaskSetManager: Lost task 106.0 in stage 17.0 (TID 1115) (localhost executor driver): TaskKilled (Stage cancelled)
2025-06-13 11:03:32,454 ERROR util.Utils: Uncaught exception in thread Executor task launch worker for task 118.0 in stage 17.0 (TID 1127)
java.lang.NullPointerException
	at org.apache.spark.scheduler.Task.$anonfun$run$2(Task.scala:152)
	at org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1419)
	at org.apache.spark.scheduler.Task.run(Task.scala:150)
	at org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:498)
	at org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1439)
	at org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:501)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-06-13 11:03:32,456 ERROR util.Utils: Uncaught exception in thread Executor task launch worker for task 120.0 in stage 17.0 (TID 1129)
java.lang.NullPointerException
	at org.apache.spark.scheduler.Task.$anonfun$run$2(Task.scala:152)
	at org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1419)
	at org.apache.spark.scheduler.Task.run(Task.scala:150)
	at org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:498)
	at org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1439)
	at org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:501)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-06-13 11:03:32,479 ERROR util.Utils: Uncaught exception in thread Executor task launch worker for task 123.0 in stage 17.0 (TID 1132)
java.lang.NullPointerException
	at org.apache.spark.scheduler.Task.$anonfun$run$2(Task.scala:152)
	at org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1419)
	at org.apache.spark.scheduler.Task.run(Task.scala:150)
	at org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:498)
	at org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1439)
	at org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:501)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-06-13 11:03:32,506 ERROR util.Utils: Uncaught exception in thread Executor task launch worker for task 126.0 in stage 17.0 (TID 1135)
java.lang.NullPointerException
	at org.apache.spark.scheduler.Task.$anonfun$run$2(Task.scala:152)
	at org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1419)
	at org.apache.spark.scheduler.Task.run(Task.scala:150)
	at org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:498)
	at org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1439)
	at org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:501)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-06-13 11:03:32,506 ERROR util.Utils: Uncaught exception in thread Executor task launch worker for task 125.0 in stage 17.0 (TID 1134)
java.lang.NullPointerException
	at org.apache.spark.scheduler.Task.$anonfun$run$2(Task.scala:152)
	at org.apache.spark.util.Utils$.tryLogNonFatalError(Utils.scala:1419)
	at org.apache.spark.scheduler.Task.run(Task.scala:150)
	at org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:498)
	at org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1439)
	at org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:501)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
