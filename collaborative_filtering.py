#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协同过滤推荐系统
基于用户-课程关系矩阵的推荐算法
"""

import mysql.connector
import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from datetime import datetime

class CollaborativeFiltering:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root', 
            'password': '123456',
            'database': 'attendance_db'
        }
    
    def get_connection(self):
        """获取MySQL连接"""
        return mysql.connector.connect(**self.mysql_config)
    
    def load_attendance_data(self):
        """加载出勤数据"""
        try:
            conn = self.get_connection()
            query = """
            SELECT student_id, student_name, course_name, attendance_status
            FROM raw_attendance_data
            """
            df = pd.read_sql(query, conn)
            conn.close()
            
            if df.empty:
                # 创建模拟数据
                data = []
                students = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
                courses = ['高等数学', '线性代数', '数据结构', '算法设计', '操作系统', '计算机网络']
                
                for student in students:
                    for course in courses:
                        attendance = np.random.choice(['A', '1'], p=[0.7, 0.3])
                        data.append({
                            'student_id': f"2021CS{students.index(student)+1:03d}",
                            'student_name': student,
                            'course_name': course,
                            'attendance_status': attendance
                        })
                df = pd.DataFrame(data)
            
            return df
        except Exception as e:
            print(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def build_user_course_matrix(self, df):
        """a. 建立用户-课程的关系矩阵"""
        # 将出勤状态转换为数值：A=1(出勤), 1=0(缺勤)
        df['rating'] = df['attendance_status'].map({'A': 1, '1': 0})
        
        # 创建用户-课程矩阵
        matrix = df.pivot_table(
            index='student_id', 
            columns='course_name', 
            values='rating', 
            fill_value=0
        )
        
        print(f"用户-课程矩阵: {matrix.shape}")
        return matrix
    
    def build_course_course_matrix(self, user_course_matrix):
        """b. 建立课程-课程的关系矩阵"""
        # 计算课程之间的相似度
        course_similarity = cosine_similarity(user_course_matrix.T)
        course_matrix = pd.DataFrame(
            course_similarity,
            index=user_course_matrix.columns,
            columns=user_course_matrix.columns
        )
        
        print(f"课程-课程矩阵: {course_matrix.shape}")
        return course_matrix
    
    def generate_user_recommendations(self, user_course_matrix, top_n=6):
        """c. 基于User-Base CF生成每个用户的推荐列表"""
        # 计算用户相似度
        user_similarity = cosine_similarity(user_course_matrix)
        user_sim_df = pd.DataFrame(
            user_similarity,
            index=user_course_matrix.index,
            columns=user_course_matrix.index
        )
        
        recommendations = []
        
        for user in user_course_matrix.index:
            # 找到相似用户
            similar_users = user_sim_df[user].sort_values(ascending=False)[1:4]
            
            # 计算推荐分数
            user_scores = {}
            for course in user_course_matrix.columns:
                if user_course_matrix.loc[user, course] == 0:  # 用户缺勤的课程
                    score = 0
                    total_sim = 0
                    
                    for sim_user, similarity in similar_users.items():
                        if user_course_matrix.loc[sim_user, course] > 0:
                            score += similarity * user_course_matrix.loc[sim_user, course]
                            total_sim += similarity
                    
                    if total_sim > 0:
                        user_scores[course] = score / total_sim
            
            # 排序并取前N个
            sorted_courses = sorted(user_scores.items(), key=lambda x: x[1], reverse=True)
            
            for rank, (course, score) in enumerate(sorted_courses[:top_n], 1):
                recommendations.append({
                    'user_id': user,
                    'course_name': course,
                    'recommendation_score': score,
                    'rank_order': rank
                })
        
        return recommendations
    
    def save_recommendations(self, recommendations):
        """保存推荐结果到数据库"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 清空旧数据
            cursor.execute("DELETE FROM user_recommendations")
            
            # 插入新推荐
            for rec in recommendations:
                sql = """
                INSERT INTO user_recommendations 
                (user_id, course_name, recommendation_score, rank_order, create_time)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    rec['user_id'],
                    rec['course_name'],
                    rec['recommendation_score'],
                    rec['rank_order'],
                    datetime.now()
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"保存了 {len(recommendations)} 条推荐记录")
        except Exception as e:
            print(f"保存推荐失败: {e}")
    
    def run_collaborative_filtering(self):
        """运行协同过滤推荐"""
        print("=== 协同过滤推荐系统 ===")
        
        # 1. 加载数据
        print("1. 加载出勤数据...")
        df = self.load_attendance_data()
        if df.empty:
            print("无数据可处理")
            return []
        
        print(f"   加载了 {len(df)} 条记录")
        
        # 2. 建立用户-课程矩阵
        print("2. 建立用户-课程关系矩阵...")
        user_course_matrix = self.build_user_course_matrix(df)
        
        # 3. 建立课程-课程矩阵
        print("3. 建立课程-课程关系矩阵...")
        course_course_matrix = self.build_course_course_matrix(user_course_matrix)
        
        # 4. 生成推荐
        print("4. 基于User-Base CF生成推荐...")
        recommendations = self.generate_user_recommendations(user_course_matrix)
        
        # 5. 保存结果
        print("5. 保存推荐结果...")
        self.save_recommendations(recommendations)
        
        print("=== 协同过滤推荐完成 ===")
        return recommendations

def main():
    """主函数 - 持续运行协同过滤推荐"""
    cf = CollaborativeFiltering()

    print("=== 协同过滤推荐服务启动 ===")
    print("每30秒更新一次推荐...")

    import time

    try:
        while True:
            print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] 开始协同过滤推荐...")

            recommendations = cf.run_collaborative_filtering()

            # 显示部分结果
            if recommendations:
                print(f"✓ 生成了 {len(recommendations)} 条推荐")
                print("推荐结果示例:")
                for i, rec in enumerate(recommendations[:5]):
                    print(f"  {i+1}. 用户: {rec['user_id']}, 课程: {rec['course_name']}, 分数: {rec['recommendation_score']:.4f}")
            else:
                print("✗ 未生成推荐结果")

            print("等待30秒后进行下一次推荐...")
            time.sleep(30)

    except KeyboardInterrupt:
        print("\n协同过滤推荐服务已停止")
    except Exception as e:
        print(f"协同过滤推荐服务出错: {e}")

if __name__ == "__main__":
    main()
