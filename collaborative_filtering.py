#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协同过滤推荐系统
基于用户-课程关系矩阵的推荐算法
"""

import mysql.connector
import pandas as pd
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from datetime import datetime

class CollaborativeFiltering:
    def __init__(self):
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root', 
            'password': '123456',
            'database': 'attendance_db'
        }
    
    def get_connection(self):
        """获取MySQL连接"""
        return mysql.connector.connect(**self.mysql_config)
    
    def load_attendance_data(self):
        """加载出勤数据"""
        try:
            conn = self.get_connection()
            query = """
            SELECT student_id, student_name, course_name, attendance_status
            FROM raw_attendance_data
            """
            df = pd.read_sql(query, conn)
            conn.close()
            
            if df.empty:
                # 创建模拟数据
                data = []
                students = ['张三', '李四', '王五', '赵六', '钱七', '孙八']
                courses = ['高等数学', '线性代数', '数据结构', '算法设计', '操作系统', '计算机网络']
                
                for student in students:
                    for course in courses:
                        attendance = np.random.choice(['A', '1'], p=[0.7, 0.3])
                        data.append({
                            'student_id': f"2021CS{students.index(student)+1:03d}",
                            'student_name': student,
                            'course_name': course,
                            'attendance_status': attendance
                        })
                df = pd.DataFrame(data)
            
            return df
        except Exception as e:
            print(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def build_user_course_matrix(self, df):
        """a. 建立用户-课程的关系矩阵"""
        # 将出勤状态转换为数值：A=1(出勤), 1=0(缺勤)
        df['rating'] = df['attendance_status'].map({'A': 1, '1': 0})
        
        # 创建用户-课程矩阵
        matrix = df.pivot_table(
            index='student_id', 
            columns='course_name', 
            values='rating', 
            fill_value=0
        )
        
        print(f"用户-课程矩阵: {matrix.shape}")
        return matrix
    
    def build_course_course_matrix(self, user_course_matrix):
        """b. 建立课程-课程的关系矩阵"""
        # 计算课程之间的相似度
        course_similarity = cosine_similarity(user_course_matrix.T)
        course_matrix = pd.DataFrame(
            course_similarity,
            index=user_course_matrix.columns,
            columns=user_course_matrix.columns
        )
        
        print(f"课程-课程矩阵: {course_matrix.shape}")
        return course_matrix
    
    def generate_user_recommendations(self, user_course_matrix, top_n=6):
        """c. 基于User-Base CF生成每个用户的推荐列表 - 推荐缺勤前6的学生"""
        # 计算用户相似度
        user_similarity = cosine_similarity(user_course_matrix)
        user_sim_df = pd.DataFrame(
            user_similarity,
            index=user_course_matrix.index,
            columns=user_course_matrix.index
        )

        recommendations = []

        # 计算每个用户的缺勤倾向分数
        user_absence_scores = {}

        for user in user_course_matrix.index:
            # 计算用户的总体缺勤率
            total_courses = len(user_course_matrix.columns)
            absent_courses = (user_course_matrix.loc[user] == 0).sum()
            absence_rate = absent_courses / total_courses

            # 找到相似用户
            similar_users = user_sim_df[user].sort_values(ascending=False)[1:4]

            # 基于相似用户计算缺勤倾向
            similarity_weighted_absence = 0
            total_similarity = 0

            for sim_user, similarity in similar_users.items():
                if similarity > 0:
                    sim_absent_courses = (user_course_matrix.loc[sim_user] == 0).sum()
                    sim_absence_rate = sim_absent_courses / total_courses
                    similarity_weighted_absence += similarity * sim_absence_rate
                    total_similarity += similarity

            if total_similarity > 0:
                predicted_absence_rate = similarity_weighted_absence / total_similarity
            else:
                predicted_absence_rate = absence_rate

            # 综合分数：当前缺勤率 + 预测缺勤率
            final_score = (absence_rate * 0.6) + (predicted_absence_rate * 0.4)
            user_absence_scores[user] = final_score

        # 按缺勤倾向排序，取前6名
        sorted_users = sorted(user_absence_scores.items(), key=lambda x: x[1], reverse=True)[:top_n]

        for rank, (user_id, score) in enumerate(sorted_users, 1):
            # 为每个高风险用户推荐最容易缺勤的课程
            user_courses = user_course_matrix.loc[user_id]
            absent_courses = user_courses[user_courses == 0].index.tolist()

            if absent_courses:
                # 推荐用户最常缺勤的课程
                recommended_course = absent_courses[0] if absent_courses else user_course_matrix.columns[0]
            else:
                # 如果用户没有缺勤记录，推荐相似用户最常缺勤的课程
                similar_users = user_sim_df[user_id].sort_values(ascending=False)[1:4]
                course_absence_count = {}
                for sim_user, _ in similar_users.items():
                    sim_absent_courses = user_course_matrix.loc[sim_user]
                    for course in sim_absent_courses[sim_absent_courses == 0].index:
                        course_absence_count[course] = course_absence_count.get(course, 0) + 1

                if course_absence_count:
                    recommended_course = max(course_absence_count.items(), key=lambda x: x[1])[0]
                else:
                    recommended_course = user_course_matrix.columns[0]

            recommendations.append({
                'user_id': user_id,
                'course_name': recommended_course,
                'recommendation_score': score,
                'rank_order': rank
            })

        return recommendations
    
    def save_recommendations(self, recommendations):
        """保存推荐结果到数据库"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 清空旧数据
            cursor.execute("DELETE FROM user_recommendations")
            
            # 插入新推荐
            for rec in recommendations:
                sql = """
                INSERT INTO user_recommendations 
                (user_id, course_name, recommendation_score, rank_order, create_time)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    rec['user_id'],
                    rec['course_name'],
                    rec['recommendation_score'],
                    rec['rank_order'],
                    datetime.now()
                ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            print(f"保存了 {len(recommendations)} 条推荐记录")
        except Exception as e:
            print(f"保存推荐失败: {e}")
    
    def run_collaborative_filtering(self):
        """运行协同过滤推荐"""
        print("=== 协同过滤推荐系统 ===")
        
        # 1. 加载数据
        print("1. 加载出勤数据...")
        df = self.load_attendance_data()
        if df.empty:
            print("无数据可处理")
            return []
        
        print(f"   加载了 {len(df)} 条记录")
        
        # 2. 建立用户-课程矩阵
        print("2. 建立用户-课程关系矩阵...")
        user_course_matrix = self.build_user_course_matrix(df)
        
        # 3. 建立课程-课程矩阵
        print("3. 建立课程-课程关系矩阵...")
        course_course_matrix = self.build_course_course_matrix(user_course_matrix)
        
        # 4. 生成推荐
        print("4. 基于User-Base CF生成推荐...")
        recommendations = self.generate_user_recommendations(user_course_matrix)
        
        # 5. 保存结果
        print("5. 保存推荐结果...")
        self.save_recommendations(recommendations)
        
        print("=== 协同过滤推荐完成 ===")
        return recommendations

def main():
    """主函数 - 按照要求的数据流：Kafka(A组)消费 -> Spark流处理 -> MySQL -> Web可视化"""
    cf = CollaborativeFiltering()

    print("=== 实时协同过滤推荐服务启动 ===")
    print("数据流: Kafka(A组)消费 -> Spark流处理 -> MySQL -> Web可视化")
    print("每5秒更新一次推荐...")

    import time

    try:
        while True:
            print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] 开始实时协同过滤推荐...")
            print("数据流: 从MySQL读取最新数据 -> 协同过滤处理 -> 保存推荐结果")

            recommendations = cf.run_collaborative_filtering()

            # 显示部分结果
            if recommendations:
                print(f"✓ 生成了 {len(recommendations)} 条推荐")
                print("推荐结果示例:")
                for i, rec in enumerate(recommendations[:5]):
                    print(f"  {i+1}. 用户: {rec['user_id']}, 课程: {rec['course_name']}, 分数: {rec['recommendation_score']:.4f}")

                # 显示缺勤前6的学生（按要求）
                print("\n个性化推荐缺勤前6的学生名单:")
                user_absence_count = {}
                for rec in recommendations:
                    if rec['user_id'] not in user_absence_count:
                        user_absence_count[rec['user_id']] = 0
                    user_absence_count[rec['user_id']] += 1

                top_6_absent = sorted(user_absence_count.items(), key=lambda x: x[1], reverse=True)[:6]
                for i, (user_id, count) in enumerate(top_6_absent, 1):
                    print(f"  {i}. 学生ID: {user_id}, 推荐课程数: {count}")
            else:
                print("✗ 未生成推荐结果")

            print("等待5秒后进行下一次推荐...")
            time.sleep(5)  # 改为5秒更新一次

    except KeyboardInterrupt:
        print("\n协同过滤推荐服务已停止")
    except Exception as e:
        print(f"协同过滤推荐服务出错: {e}")

if __name__ == "__main__":
    main()
