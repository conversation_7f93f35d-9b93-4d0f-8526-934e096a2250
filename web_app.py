#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web前端应用
实现用户注册登录、数据可视化展示
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import mysql.connector
from werkzeug.security import generate_password_hash, check_password_hash
import json
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 在生产环境中应该使用更安全的密钥

class DatabaseManager:
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',  # 根据需求文档配置
            'database': 'attendance_db'
        }
    
    def get_connection(self):
        return mysql.connector.connect(**self.config)
    
    def execute_query(self, query, params=None, fetch=True):
        """执行SQL查询"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
            else:
                conn.commit()
                result = cursor.rowcount
            
            cursor.close()
            conn.close()
            return result
            
        except Exception as e:
            print(f"数据库操作失败: {e}")
            return None

db = DatabaseManager()

@app.route('/')
def index():
    """首页"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html', username=session.get('username'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # 查询用户
        user = db.execute_query(
            "SELECT * FROM users WHERE username = %s", 
            (username,)
        )
        
        if user and len(user) > 0:
            # 简化密码验证（实际应用中应该使用哈希验证）
            if user[0]['password'] == password:
                session['user_id'] = user[0]['id']
                session['username'] = user[0]['username']
                session['role'] = user[0]['role']
                return redirect(url_for('index'))
            else:
                return render_template('login.html', error='密码错误')
        else:
            return render_template('login.html', error='用户不存在')
    
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """注册页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        email = request.form['email']
        real_name = request.form['real_name']
        
        # 检查用户是否已存在
        existing_user = db.execute_query(
            "SELECT * FROM users WHERE username = %s", 
            (username,)
        )
        
        if existing_user and len(existing_user) > 0:
            return render_template('register.html', error='用户名已存在')
        
        # 插入新用户
        result = db.execute_query(
            """INSERT INTO users (username, password, email, real_name) 
               VALUES (%s, %s, %s, %s)""",
            (username, password, email, real_name),
            fetch=False
        )
        
        if result:
            return redirect(url_for('login'))
        else:
            return render_template('register.html', error='注册失败')
    
    return render_template('register.html')

@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    return redirect(url_for('login'))

@app.route('/api/attendance_summary')
def api_attendance_summary():
    """获取总体出勤统计数据"""
    # 优先读取最新的有效数据（不是0,0的记录）
    data = db.execute_query("""
        SELECT * FROM attendance_summary
        WHERE total_present > 0 OR total_absent > 0
        ORDER BY update_time DESC
        LIMIT 1
    """)

    # 如果没有有效数据，读取最新记录
    if not data or len(data) == 0:
        data = db.execute_query("SELECT * FROM attendance_summary ORDER BY update_time DESC LIMIT 1")

    if data and len(data) > 0:
        return jsonify({
            'total_present': data[0]['total_present'],
            'total_absent': data[0]['total_absent']
        })
    else:
        return jsonify({'total_present': 0, 'total_absent': 0})

@app.route('/api/course_top5')
def api_course_top5():
    """获取课程出勤TOP5数据"""
    data = db.execute_query("""
        SELECT course_name, present_count, absent_count, 
               ROUND(present_count * 100.0 / (present_count + absent_count), 2) as attendance_rate
        FROM course_attendance 
        WHERE (present_count + absent_count) > 0
        ORDER BY attendance_rate DESC, present_count DESC 
        LIMIT 5
    """)
    
    return jsonify(data if data else [])

@app.route('/api/class_attendance')
def api_class_attendance():
    """获取班级出勤统计数据"""
    data = db.execute_query("""
        SELECT class_number, present_count, absent_count,
               ROUND(present_count * 100.0 / (present_count + absent_count), 2) as attendance_rate
        FROM class_attendance 
        WHERE (present_count + absent_count) > 0
        ORDER BY attendance_rate DESC
    """)
    
    return jsonify(data if data else [])



@app.route('/api/personalized_recommendations')
def api_personalized_recommendations():
    """获取协同过滤推荐（基于用户行为相似度）"""
    limit = request.args.get('limit', 10, type=int)

    # 首先尝试从协同过滤结果表获取数据
    cf_data = db.execute_query("""
        SELECT ur.user_id as student_id, r.student_name, ur.recommendation_score,
               COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) as absence_count
        FROM user_recommendations ur
        LEFT JOIN raw_attendance_data r ON ur.user_id = r.student_id
        GROUP BY ur.user_id, r.student_name, ur.recommendation_score
        ORDER BY ur.recommendation_score DESC
        LIMIT %s
    """, (limit,))

    # 如果协同过滤表为空，使用基于出勤行为的推荐
    if not cf_data:
        data = db.execute_query("""
            SELECT DISTINCT r.student_id, r.student_name,
                   COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) as absence_count,
                   ROUND(COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) * 100.0 / COUNT(*), 2) as absence_rate
            FROM raw_attendance_data r
            GROUP BY r.student_id, r.student_name
            HAVING COUNT(*) > 0
            ORDER BY absence_count DESC, absence_rate DESC
            LIMIT %s
        """, (limit,))

        # 如果没有数据，返回空列表（所有数据必须来自MySQL）
        if not data:
            return jsonify([])

        return jsonify(data)

    return jsonify(cf_data)

@app.route('/api/offline_recommendations')
def api_offline_recommendations():
    """获取离线推荐（缺席最多的前N名学生）"""
    limit = request.args.get('limit', 10, type=int)

    # 首先尝试从学生缺勤排名表获取数据
    data = db.execute_query("""
        SELECT student_id, student_name, total_absence, absence_rate, rank_order
        FROM student_absence_ranking
        ORDER BY rank_order ASC
        LIMIT %s
    """, (limit,))

    # 如果排名表为空，从原始数据计算
    if not data:
        data = db.execute_query("""
            SELECT DISTINCT r.student_id, r.student_name,
                   COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) as total_absence,
                   ROUND(COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) * 100.0 / COUNT(*), 2) as absence_rate,
                   ROW_NUMBER() OVER (ORDER BY COUNT(CASE WHEN r.attendance_status = '1' THEN 1 END) DESC) as rank_order
            FROM raw_attendance_data r
            GROUP BY r.student_id, r.student_name
            HAVING COUNT(*) > 0
            ORDER BY total_absence DESC, absence_rate DESC
            LIMIT %s
        """, (limit,))

        # 如果没有数据，返回空列表（所有数据必须来自MySQL）
        if not data:
            return jsonify([])

    return jsonify(data)

@app.route('/api/offline_class_stats')
def api_offline_class_stats():
    """获取离线RDD处理的班级统计数据"""
    data = db.execute_query("""
        SELECT class_number, present_count, absent_count,
               ROUND(present_count * 100.0 / (present_count + absent_count), 2) as attendance_rate,
               update_time
        FROM class_attendance_rdd
        ORDER BY attendance_rate DESC
    """)

    return jsonify(data if data else [])

@app.route('/api/offline_course_stats')
def api_offline_course_stats():
    """获取离线SQL处理的课程统计数据"""
    data = db.execute_query("""
        SELECT course_name, present_count, absent_count, total_count, attendance_rate, update_time
        FROM course_attendance_sql
        ORDER BY attendance_rate DESC
    """)

    return jsonify(data if data else [])

@app.route('/api/system_status')
def api_system_status():
    """获取系统状态信息"""
    try:
        # 检查各个数据表的记录数
        raw_data_count = db.execute_query("SELECT COUNT(*) as count FROM raw_attendance_data")
        cf_recommendations_count = db.execute_query("SELECT COUNT(*) as count FROM user_recommendations")
        offline_rankings_count = db.execute_query("SELECT COUNT(*) as count FROM student_absence_ranking")

        # 获取最后更新时间
        last_raw_update = db.execute_query("SELECT MAX(record_time) as last_update FROM raw_attendance_data")
        last_cf_update = db.execute_query("SELECT MAX(create_time) as last_update FROM user_recommendations")
        last_ranking_update = db.execute_query("SELECT MAX(update_time) as last_update FROM student_absence_ranking")

        status = {
            'system_status': 'running',
            'data_counts': {
                'raw_data': raw_data_count[0]['count'] if raw_data_count else 0,
                'cf_recommendations': cf_recommendations_count[0]['count'] if cf_recommendations_count else 0,
                'offline_rankings': offline_rankings_count[0]['count'] if offline_rankings_count else 0
            },
            'last_updates': {
                'raw_data': str(last_raw_update[0]['last_update']) if last_raw_update and last_raw_update[0]['last_update'] else None,
                'cf_recommendations': str(last_cf_update[0]['last_update']) if last_cf_update and last_cf_update[0]['last_update'] else None,
                'offline_rankings': str(last_ranking_update[0]['last_update']) if last_ranking_update and last_ranking_update[0]['last_update'] else None
            },
            'processing_status': {
                'kafka_producer': 'unknown',  # 可以通过检查进程或日志文件来确定
                'spark_streaming': 'unknown',
                'collaborative_filtering': 'completed' if cf_recommendations_count and cf_recommendations_count[0]['count'] > 0 else 'pending'
            }
        }

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'system_status': 'error',
            'error': str(e)
        }), 500



@app.route('/api/search_student')
def api_search_student():
    """搜索学生并预测可能缺席的课程"""
    student_name = request.args.get('student_name', '')
    
    if not student_name:
        return jsonify({'error': '请提供学生姓名'})
    
    # 查找学生信息
    student_info = db.execute_query("""
        SELECT DISTINCT student_id, student_name 
        FROM raw_attendance_data 
        WHERE student_name LIKE %s
    """, (f'%{student_name}%',))
    
    if not student_info:
        return jsonify({'error': '未找到该学生'})
    
    student_id = student_info[0]['student_id']
    
    # 获取该学生的出勤记录
    attendance_data = db.execute_query("""
        SELECT course_name, attendance_status, COUNT(*) as count
        FROM raw_attendance_data 
        WHERE student_id = %s
        GROUP BY course_name, attendance_status
    """, (student_id,))
    
    # 简单的预测逻辑：基于历史缺勤率预测
    course_absence_rate = {}
    for record in attendance_data:
        course = record['course_name']
        if course not in course_absence_rate:
            course_absence_rate[course] = {'total': 0, 'absent': 0}
        
        course_absence_rate[course]['total'] += record['count']
        if record['attendance_status'] == '1':
            course_absence_rate[course]['absent'] += record['count']
    
    # 计算缺勤率并排序
    predictions = []
    for course, stats in course_absence_rate.items():
        if stats['total'] > 0:
            absence_rate = stats['absent'] / stats['total']
            predictions.append({
                'course_name': course,
                'absence_rate': round(absence_rate * 100, 2),
                'prediction_score': absence_rate
            })
    
    # 按缺勤率排序，取前3个
    predictions.sort(key=lambda x: x['prediction_score'], reverse=True)
    top3_predictions = predictions[:3]
    
    return jsonify({
        'student_info': student_info[0],
        'predictions': top3_predictions
    })

@app.route('/realtime')
def realtime():
    """21) 实时界面html"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('realtime.html')

@app.route('/offline')
def offline():
    """22) 离线界面html"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('offline.html')

@app.route('/recommendations')
def recommendations():
    """23) 实时推荐界面html"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('recommendations.html')

@app.route('/offline_recommendations')
def offline_recommendations():
    """24) 离线推荐界面html"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('offline_recommendations.html')

@app.route('/search')
def search():
    """25) 通过搜索展示目标学生功能html"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('search.html')

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5001)
