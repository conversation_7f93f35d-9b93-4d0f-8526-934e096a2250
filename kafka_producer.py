#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kafka生产者 - 学生出勤数据
每隔5秒推送数据到attendance主题
格式：班级号 学生名字 课程名 学号 分数 是否出勤
"""

import time
import random
import os
import pymysql
from datetime import datetime

class AttendanceProducer:
    def __init__(self):
        self.topic_a = 'attendance_realtime'  # A组：实时处理
        self.topic_b = 'attendance_offline'   # B组：离线处理

        # 创建消息队列目录
        os.makedirs('kafka_queue', exist_ok=True)

        # MySQL配置
        self.mysql_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'attendance_db',
            'charset': 'utf8mb4'
        }

        # 基础数据 - 使用随机数+数组形式
        self.classes = ['CS2021', 'CS2022', 'EE2021', 'EE2022', 'ME2021', 'BM2021']
        self.students = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二']
        self.courses = ['高等数学', '线性代数', '概率论', '数据结构', '算法设计', '操作系统', '计算机网络', '数据库原理']

        # 学号映射
        self.student_ids = {
            '张三': '2021CS001', '李四': '2021CS002', '王五': '2021EE001', '赵六': '2022CS001',
            '钱七': '2021EE002', '孙八': '2022CS002', '周九': '2021CS003', '吴十': '2022EE001',
            '郑十一': '2021ME001', '王十二': '2021BM001'
        }

    def create_mysql_connection(self):
        """创建MySQL连接"""
        try:
            connection = pymysql.connect(**self.mysql_config)
            return connection
        except Exception as e:
            print(f"✗ MySQL连接失败: {e}")
            return None

    def generate_record(self):
        """生成随机出勤记录"""
        class_num = random.choice(self.classes)
        student_name = random.choice(self.students)
        course_name = random.choice(self.courses)
        student_id = self.student_ids[student_name]

        # 随机生成出勤状态：A出勤(70%), 1缺勤(30%)
        attendance = random.choices(['A', '1'], weights=[0.7, 0.3])[0]
        score = 100 if attendance == 'A' else 0

        # 格式：班级号 学生名字 课程名 学号 分数 是否出勤
        return f"{class_num} {student_name} {course_name} {student_id} {score} {attendance}"

    def send_data(self, data):
        """发送数据到Kafka"""
        try:
            future = self.producer.send(self.topic, value=data)
            future.get(timeout=10)
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送: {data}")
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False

    def start_producing(self):
        """开始生产数据"""
        if not self.create_producer():
            return

        print(f"开始每5秒推送数据到主题 '{self.topic}'...")
        print("按Ctrl+C停止")

        try:
            count = 0
            while True:
                record = self.generate_record()
                if self.send_data(record):
                    count += 1
                    if count % 10 == 0:
                        print(f"已发送 {count} 条记录")

                time.sleep(5)  # 每5秒发送一次

        except KeyboardInterrupt:
            print("\n停止数据生产")
        finally:
            if self.producer:
                self.producer.close()

def main():
    producer = AttendanceProducer()
    producer.start_producing()

if __name__ == "__main__":
    main()
